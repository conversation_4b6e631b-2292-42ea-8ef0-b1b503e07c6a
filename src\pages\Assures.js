import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { assureAPI } from '../services/api';
import LoadingSpinner from '../components/LoadingSpinner';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline';

const Assures = () => {
  const [assures, setAssures] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchAssures();
  }, [currentPage, searchTerm]);

  const fetchAssures = async () => {
    try {
      setLoading(true);
      const response = await assureAPI.getAll({
        page: currentPage,
        per_page: 20,
        search: searchTerm,
      });
      
      setAssures(response.data.assures);
      setTotalPages(response.data.pagination.pages);
    } catch (err) {
      setError(err.response?.data?.message || 'Erreur lors du chargement des assurés');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleDelete = async (assure) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer l'assuré ${assure.ass_mat}-${assure.ass_cle}?`)) {
      try {
        await assureAPI.delete(assure.ass_mat);
        fetchAssures();
      } catch (err) {
        alert(err.response?.data?.message || 'Erreur lors de la suppression');
      }
    }
  };

  if (loading && assures.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Assurés</h1>
          <p className="mt-1 text-sm text-secondary-600">
            Gérez les assurés du système CNSS
          </p>
        </div>
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="btn-primary flex items-center"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          Nouvel Assuré
        </motion.button>
      </div>

      {/* Search */}
      <div className="card">
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-secondary-400" />
            <input
              type="text"
              placeholder="Rechercher par matricule, IU, CIN..."
              value={searchTerm}
              onChange={handleSearch}
              className="input-field pl-10"
            />
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-error-50 border border-error-200 rounded-lg p-4">
          <p className="text-error-600">{error}</p>
        </div>
      )}

      {/* Table */}
      <div className="card p-0">
        <div className="table-container">
          <table className="min-w-full divide-y divide-secondary-200">
            <thead className="bg-secondary-50">
              <tr>
                <th className="table-header">Matricule</th>
                <th className="table-header">IU</th>
                <th className="table-header">Employeur</th>
                <th className="table-header">Date Effet</th>
                <th className="table-header">Date Immatriculation</th>
                <th className="table-header">Bureau</th>
                <th className="table-header">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-secondary-200">
              <AnimatePresence>
                {assures.map((assure, index) => (
                  <motion.tr
                    key={`${assure.ass_mat}-${assure.ass_cle}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.05 }}
                    className="hover:bg-secondary-50"
                  >
                    <td className="table-cell">
                      <div className="flex items-center">
                        <UserGroupIcon className="h-5 w-5 text-secondary-400 mr-2" />
                        <span className="font-medium">
                          {assure.ass_mat}-{assure.ass_cle}
                        </span>
                      </div>
                    </td>
                    <td className="table-cell">
                      <span className="font-medium text-primary-600">
                        {assure.ass_iu || '-'}
                      </span>
                    </td>
                    <td className="table-cell">
                      <span className="text-sm text-secondary-600">
                        {assure.emp_mat ? `${assure.emp_mat}-${assure.emp_cle}` : '-'}
                      </span>
                    </td>
                    <td className="table-cell">
                      <span className="text-sm text-secondary-600">
                        {assure.ass_dteff ? new Date(assure.ass_dteff).toLocaleDateString('fr-FR') : '-'}
                      </span>
                    </td>
                    <td className="table-cell">
                      <span className="text-sm text-secondary-600">
                        {assure.ass_dtimmat ? new Date(assure.ass_dtimmat).toLocaleDateString('fr-FR') : '-'}
                      </span>
                    </td>
                    <td className="table-cell">
                      <span className="text-sm text-secondary-600">
                        {assure.bur_cod || '-'}
                      </span>
                    </td>
                    <td className="table-cell">
                      <div className="flex items-center space-x-2">
                        <button
                          className="p-1 text-secondary-400 hover:text-primary-600 transition-colors"
                          title="Voir"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        <button
                          className="p-1 text-secondary-400 hover:text-warning-600 transition-colors"
                          title="Modifier"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(assure)}
                          className="p-1 text-secondary-400 hover:text-error-600 transition-colors"
                          title="Supprimer"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </AnimatePresence>
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-secondary-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-secondary-600">
                Page {currentPage} sur {totalPages}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Précédent
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Suivant
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Loading overlay */}
      {loading && assures.length > 0 && (
        <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center z-50">
          <LoadingSpinner size="large" />
        </div>
      )}
    </div>
  );
};

export default Assures;
