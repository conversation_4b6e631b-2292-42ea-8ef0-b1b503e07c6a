from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models.api_endpoint import APIEndpoint, APICall
from app.models.user import User
from app.utils.decorators import agent_or_admin_required, audit_log, validate_json
from datetime import datetime
from sqlalchemy import or_, and_
import requests
import time

api_mgmt_bp = Blueprint('api_management', __name__)

@api_mgmt_bp.route('/my-apis', methods=['GET'])
@agent_or_admin_required
@audit_log('READ', 'api_endpoint')
def get_my_apis():
    """Get APIs created by current user's organization"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '', type=str)
    
    # Build query for user's organization APIs
    query = APIEndpoint.query.filter_by(organization=user.organization)
    
    # Apply search filter
    if search:
        query = query.filter(
            or_(
                APIEndpoint.name.ilike(f'%{search}%'),
                APIEndpoint.description.ilike(f'%{search}%'),
                APIEndpoint.endpoint_url.ilike(f'%{search}%')
            )
        )
    
    # Apply pagination
    apis = query.paginate(page=page, per_page=per_page, error_out=False)
    
    return jsonify({
        'message': 'APIs retrieved successfully',
        'apis': [api.to_dict() for api in apis.items],
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': apis.total,
            'pages': apis.pages,
            'has_next': apis.has_next,
            'has_prev': apis.has_prev
        }
    }), 200

@api_mgmt_bp.route('/public-apis', methods=['GET'])
@agent_or_admin_required
@audit_log('READ', 'api_endpoint')
def get_public_apis():
    """Get all public APIs from other organizations"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '', type=str)
    organization = request.args.get('organization', '', type=str)
    
    # Build query for public APIs (excluding user's own organization)
    query = APIEndpoint.query.filter(
        and_(
            APIEndpoint.is_public == True,
            APIEndpoint.is_active == True,
            APIEndpoint.organization != user.organization
        )
    )
    
    # Apply organization filter
    if organization:
        query = query.filter(APIEndpoint.organization.ilike(f'%{organization}%'))
    
    # Apply search filter
    if search:
        query = query.filter(
            or_(
                APIEndpoint.name.ilike(f'%{search}%'),
                APIEndpoint.description.ilike(f'%{search}%'),
                APIEndpoint.organization.ilike(f'%{search}%')
            )
        )
    
    # Apply pagination
    apis = query.paginate(page=page, per_page=per_page, error_out=False)
    
    return jsonify({
        'message': 'Public APIs retrieved successfully',
        'apis': [api.to_dict() for api in apis.items],
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': apis.total,
            'pages': apis.pages,
            'has_next': apis.has_next,
            'has_prev': apis.has_prev
        }
    }), 200

@api_mgmt_bp.route('/apis', methods=['POST'])
@agent_or_admin_required
@validate_json('name', 'endpoint_url', 'method')
@audit_log('CREATE', 'api_endpoint')
def create_api():
    """Create new API endpoint"""
    data = request.get_json()
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    # Check if API with same URL and method already exists for this organization
    existing = APIEndpoint.query.filter_by(
        endpoint_url=data['endpoint_url'],
        method=data['method'],
        organization=user.organization
    ).first()
    
    if existing:
        return jsonify({'message': 'API endpoint already exists for this organization'}), 400
    
    # Create new API endpoint
    api = APIEndpoint(
        name=data['name'],
        description=data.get('description', ''),
        endpoint_url=data['endpoint_url'],
        method=data['method'].upper(),
        organization=user.organization,
        created_by=current_user_id,
        is_public=data.get('is_public', True),
        auth_required=data.get('auth_required', True)
    )
    
    # Set schemas if provided
    if data.get('request_schema'):
        api.set_request_schema(data['request_schema'])
    if data.get('response_schema'):
        api.set_response_schema(data['response_schema'])
    if data.get('headers_required'):
        api.set_headers_required(data['headers_required'])
    
    db.session.add(api)
    db.session.commit()
    
    return jsonify({
        'message': 'API endpoint created successfully',
        'api': api.to_dict()
    }), 201

@api_mgmt_bp.route('/apis/<int:api_id>', methods=['GET'])
@agent_or_admin_required
@audit_log('READ', 'api_endpoint')
def get_api(api_id):
    """Get specific API endpoint details"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    api = APIEndpoint.query.get_or_404(api_id)
    
    # Check if user can access this API
    if not api.is_public and api.organization != user.organization:
        return jsonify({'message': 'Access denied to this API'}), 403
    
    return jsonify({
        'message': 'API endpoint retrieved successfully',
        'api': api.to_dict()
    }), 200

@api_mgmt_bp.route('/apis/<int:api_id>', methods=['PUT'])
@agent_or_admin_required
@validate_json('name')
@audit_log('UPDATE', 'api_endpoint')
def update_api(api_id):
    """Update API endpoint (only by creator's organization)"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    api = APIEndpoint.query.get_or_404(api_id)
    
    # Check if user's organization owns this API
    if api.organization != user.organization:
        return jsonify({'message': 'You can only update APIs from your organization'}), 403
    
    data = request.get_json()
    
    # Update fields
    api.name = data.get('name', api.name)
    api.description = data.get('description', api.description)
    api.endpoint_url = data.get('endpoint_url', api.endpoint_url)
    api.method = data.get('method', api.method).upper()
    api.is_public = data.get('is_public', api.is_public)
    api.is_active = data.get('is_active', api.is_active)
    api.auth_required = data.get('auth_required', api.auth_required)
    
    # Update schemas if provided
    if 'request_schema' in data:
        api.set_request_schema(data['request_schema'])
    if 'response_schema' in data:
        api.set_response_schema(data['response_schema'])
    if 'headers_required' in data:
        api.set_headers_required(data['headers_required'])
    
    api.updated_at = datetime.utcnow()
    db.session.commit()
    
    return jsonify({
        'message': 'API endpoint updated successfully',
        'api': api.to_dict()
    }), 200

@api_mgmt_bp.route('/apis/<int:api_id>', methods=['DELETE'])
@agent_or_admin_required
@audit_log('DELETE', 'api_endpoint')
def delete_api(api_id):
    """Delete API endpoint (only by creator's organization)"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    api = APIEndpoint.query.get_or_404(api_id)
    
    # Check if user's organization owns this API
    if api.organization != user.organization:
        return jsonify({'message': 'You can only delete APIs from your organization'}), 403
    
    db.session.delete(api)
    db.session.commit()
    
    return jsonify({'message': 'API endpoint deleted successfully'}), 200

@api_mgmt_bp.route('/apis/<int:api_id>/call', methods=['POST'])
@agent_or_admin_required
@audit_log('CALL', 'api_endpoint')
def call_api(api_id):
    """Call an API endpoint and log the request"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    api = APIEndpoint.query.get_or_404(api_id)
    
    # Check if user can access this API
    if not api.is_public and api.organization != user.organization:
        return jsonify({'message': 'Access denied to this API'}), 403
    
    if not api.is_active:
        return jsonify({'message': 'API endpoint is not active'}), 400
    
    data = request.get_json() or {}
    
    # Prepare request
    headers = data.get('headers', {})
    params = data.get('params', {})
    body = data.get('body', {})
    
    # Add authentication headers if required
    if api.auth_required:
        headers['Authorization'] = request.headers.get('Authorization', '')
    
    start_time = time.time()
    
    try:
        # Make the API call
        if api.method == 'GET':
            response = requests.get(api.endpoint_url, headers=headers, params=params, timeout=30)
        elif api.method == 'POST':
            response = requests.post(api.endpoint_url, headers=headers, params=params, json=body, timeout=30)
        elif api.method == 'PUT':
            response = requests.put(api.endpoint_url, headers=headers, params=params, json=body, timeout=30)
        elif api.method == 'DELETE':
            response = requests.delete(api.endpoint_url, headers=headers, params=params, timeout=30)
        else:
            return jsonify({'message': 'Unsupported HTTP method'}), 400
        
        response_time_ms = int((time.time() - start_time) * 1000)
        
        # Try to parse JSON response
        try:
            response_data = response.json()
        except:
            response_data = response.text
        
        # Log the API call
        api_call = APICall(
            api_endpoint_id=api_id,
            caller_user_id=current_user_id,
            caller_organization=user.organization,
            request_method=api.method,
            response_status=response.status_code,
            response_time_ms=response_time_ms,
            ip_address=request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
        )
        
        api_call.set_request_headers(headers)
        api_call.set_request_body({'params': params, 'body': body})
        
        db.session.add(api_call)
        
        # Update API call statistics
        api.increment_calls()
        
        return jsonify({
            'message': 'API call completed successfully',
            'response': {
                'status_code': response.status_code,
                'data': response_data,
                'response_time_ms': response_time_ms
            }
        }), 200
        
    except requests.exceptions.RequestException as e:
        response_time_ms = int((time.time() - start_time) * 1000)
        
        # Log failed API call
        api_call = APICall(
            api_endpoint_id=api_id,
            caller_user_id=current_user_id,
            caller_organization=user.organization,
            request_method=api.method,
            response_status=500,
            response_time_ms=response_time_ms,
            ip_address=request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
        )
        
        api_call.set_request_headers(headers)
        api_call.set_request_body({'params': params, 'body': body})
        
        db.session.add(api_call)
        db.session.commit()
        
        return jsonify({
            'message': 'API call failed',
            'error': str(e),
            'response_time_ms': response_time_ms
        }), 500

@api_mgmt_bp.route('/stats', methods=['GET'])
@agent_or_admin_required
@audit_log('READ', 'api_stats')
def get_api_stats():
    """Get API usage statistics"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    # My organization's API stats
    my_apis_count = APIEndpoint.query.filter_by(organization=user.organization).count()
    my_active_apis = APIEndpoint.query.filter_by(organization=user.organization, is_active=True).count()
    
    # Total calls to my APIs
    my_api_calls = db.session.query(db.func.sum(APIEndpoint.total_calls)).filter_by(organization=user.organization).scalar() or 0
    
    # My calls to other APIs
    my_calls_made = APICall.query.filter_by(caller_user_id=current_user_id).count()
    
    # Public APIs available
    public_apis_count = APIEndpoint.query.filter(
        and_(APIEndpoint.is_public == True, APIEndpoint.organization != user.organization)
    ).count()
    
    return jsonify({
        'message': 'API statistics retrieved successfully',
        'stats': {
            'my_apis': {
                'total': my_apis_count,
                'active': my_active_apis,
                'total_calls_received': my_api_calls
            },
            'usage': {
                'calls_made': my_calls_made,
                'public_apis_available': public_apis_count
            }
        }
    }), 200
