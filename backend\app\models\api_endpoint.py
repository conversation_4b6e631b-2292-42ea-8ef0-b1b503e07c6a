from app import db
from datetime import datetime
import json

class APIEndpoint(db.Model):
    """
    Model for managing API endpoints that organizations can create and share
    """
    __tablename__ = 'api_endpoints'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # API Information
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    endpoint_url = db.Column(db.String(200), nullable=False)
    method = db.Column(db.String(10), nullable=False, default='GET')  # GET, POST, PUT, DELETE
    
    # Organization and Access Control
    organization = db.Column(db.String(100), nullable=False)
    created_by = db.Column(db.Integer, db.<PERSON>Key('users.id'), nullable=False)
    is_public = db.Column(db.Boolean, default=True)  # If false, only organization can access
    is_active = db.Column(db.<PERSON>, default=True)
    
    # API Configuration
    request_schema = db.Column(db.Text)  # JSON schema for request validation
    response_schema = db.Column(db.Text)  # JSON schema for response format
    headers_required = db.Column(db.Text)  # JSON array of required headers
    auth_required = db.Column(db.Boolean, default=True)
    
    # Usage Statistics
    total_calls = db.Column(db.Integer, default=0)
    last_called = db.Column(db.DateTime)
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    creator = db.relationship('User', backref='created_apis')
    api_calls = db.relationship('APICall', backref='endpoint', lazy='dynamic')
    
    def set_request_schema(self, schema):
        """Set request schema as JSON string"""
        if schema:
            self.request_schema = json.dumps(schema, default=str)
    
    def get_request_schema(self):
        """Get request schema as Python object"""
        if self.request_schema:
            try:
                return json.loads(self.request_schema)
            except json.JSONDecodeError:
                return None
        return None
    
    def set_response_schema(self, schema):
        """Set response schema as JSON string"""
        if schema:
            self.response_schema = json.dumps(schema, default=str)
    
    def get_response_schema(self):
        """Get response schema as Python object"""
        if self.response_schema:
            try:
                return json.loads(self.response_schema)
            except json.JSONDecodeError:
                return None
        return None
    
    def set_headers_required(self, headers):
        """Set required headers as JSON string"""
        if headers:
            self.headers_required = json.dumps(headers, default=str)
    
    def get_headers_required(self):
        """Get required headers as Python object"""
        if self.headers_required:
            try:
                return json.loads(self.headers_required)
            except json.JSONDecodeError:
                return None
        return None
    
    def increment_calls(self):
        """Increment API call counter"""
        self.total_calls += 1
        self.last_called = datetime.utcnow()
        db.session.commit()
    
    def to_dict(self):
        """Convert API endpoint to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'endpoint_url': self.endpoint_url,
            'method': self.method,
            'organization': self.organization,
            'created_by': self.created_by,
            'creator_name': self.creator.username if self.creator else None,
            'is_public': self.is_public,
            'is_active': self.is_active,
            'request_schema': self.get_request_schema(),
            'response_schema': self.get_response_schema(),
            'headers_required': self.get_headers_required(),
            'auth_required': self.auth_required,
            'total_calls': self.total_calls,
            'last_called': self.last_called.isoformat() if self.last_called else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f'<APIEndpoint {self.name}: {self.method} {self.endpoint_url}>'


class APICall(db.Model):
    """
    Model for tracking API calls and usage statistics
    """
    __tablename__ = 'api_calls'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # API Call Information
    api_endpoint_id = db.Column(db.Integer, db.ForeignKey('api_endpoints.id'), nullable=False)
    caller_user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    caller_organization = db.Column(db.String(100))
    
    # Request Details
    request_method = db.Column(db.String(10))
    request_headers = db.Column(db.Text)  # JSON string
    request_body = db.Column(db.Text)  # JSON string
    request_params = db.Column(db.Text)  # JSON string
    
    # Response Details
    response_status = db.Column(db.Integer)
    response_body = db.Column(db.Text)  # JSON string
    response_time_ms = db.Column(db.Integer)  # Response time in milliseconds
    
    # Metadata
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    caller = db.relationship('User', backref='api_calls_made')
    
    def set_request_headers(self, headers):
        """Set request headers as JSON string"""
        if headers:
            self.request_headers = json.dumps(dict(headers), default=str)
    
    def get_request_headers(self):
        """Get request headers as Python object"""
        if self.request_headers:
            try:
                return json.loads(self.request_headers)
            except json.JSONDecodeError:
                return None
        return None
    
    def set_request_body(self, body):
        """Set request body as JSON string"""
        if body:
            self.request_body = json.dumps(body, default=str)
    
    def get_request_body(self):
        """Get request body as Python object"""
        if self.request_body:
            try:
                return json.loads(self.request_body)
            except json.JSONDecodeError:
                return None
        return None
    
    def to_dict(self):
        """Convert API call to dictionary"""
        return {
            'id': self.id,
            'api_endpoint_id': self.api_endpoint_id,
            'endpoint_name': self.endpoint.name if self.endpoint else None,
            'caller_user_id': self.caller_user_id,
            'caller_username': self.caller.username if self.caller else None,
            'caller_organization': self.caller_organization,
            'request_method': self.request_method,
            'request_headers': self.get_request_headers(),
            'request_body': self.get_request_body(),
            'response_status': self.response_status,
            'response_time_ms': self.response_time_ms,
            'ip_address': self.ip_address,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None
        }
    
    def __repr__(self):
        return f'<APICall {self.id}: {self.request_method} - {self.response_status}>'
