#!/usr/bin/env python3
"""
CNSS Application - Main Flask Application Entry Point
"""

import os
from app import create_app, db
from app.models import User, Employeur, <PERSON><PERSON>, Beneficiaire, AuditLog
from flask_migrate import upgrade

def create_tables():
    """Create database tables"""
    db.create_all()

def create_admin_user():
    """Create default admin user if it doesn't exist"""
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(
            username='admin',
            email='<EMAIL>',
            role='admin',
            organization='CNSS'
        )
        admin.set_password('admin123')  # Change this in production!
        db.session.add(admin)
        db.session.commit()
        print("Default admin user created: admin/admin123")

def create_sample_agent():
    """Create a sample agent user for testing"""
    agent = User.query.filter_by(username='agent1').first()
    if not agent:
        agent = User(
            username='agent1',
            email='<EMAIL>',
            role='agent',
            organization='CNSS Casablanca'
        )
        agent.set_password('agent123')
        db.session.add(agent)
        db.session.commit()
        print("Sample agent user created: agent1/agent123")

app = create_app()

@app.shell_context_processor
def make_shell_context():
    """Make database models available in flask shell"""
    return {
        'db': db,
        'User': User,
        'Employeur': Employeur,
        'Assure': Assure,
        'Beneficiaire': Beneficiaire,
        'AuditLog': AuditLog
    }

@app.before_first_request
def initialize_database():
    """Initialize database on first request"""
    create_tables()
    create_admin_user()
    create_sample_agent()

if __name__ == '__main__':
    with app.app_context():
        # Create tables and default users
        create_tables()
        create_admin_user()
        create_sample_agent()
    
    # Run the application
    app.run(
        host=os.environ.get('FLASK_HOST', '0.0.0.0'),
        port=int(os.environ.get('FLASK_PORT', 5000)),
        debug=os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    )
