#!/usr/bin/env python3
"""
CNSS Application - Main Flask Application Entry Point
"""

import os
from app import create_app, db
from app.models import User, AuditLog, APIEndpoint, APICall
from flask_migrate import upgrade

def create_tables():
    """Create database tables"""
    db.create_all()

def create_admin_user():
    """Create default admin user if it doesn't exist"""
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(
            username='admin',
            email='<EMAIL>',
            role='admin',
            organization='CNSS'
        )
        admin.set_password('admin123')  # Change this in production!
        db.session.add(admin)
        db.session.commit()
        print("Default admin user created: admin/admin123")

def create_sample_agent():
    """Create a sample agent user for testing"""
    agent = User.query.filter_by(username='agent1').first()
    if not agent:
        agent = User(
            username='agent1',
            email='<EMAIL>',
            role='agent',
            organization='CNSS Casablanca'
        )
        agent.set_password('agent123')
        db.session.add(agent)
        db.session.commit()
        print("Sample agent user created: agent1/agent123")

def create_sample_apis():
    """Create sample API endpoints"""
    # Check if sample APIs already exist
    if APIEndpoint.query.count() > 0:
        return

    # Get users for API creation
    admin = User.query.filter_by(username='admin').first()
    agent = User.query.filter_by(username='agent1').first()

    if admin:
        # Sample APIs from CNSS Central
        api1 = APIEndpoint(
            name='Employee Verification API',
            description='Verify employee information and status',
            endpoint_url='https://jsonplaceholder.typicode.com/users',
            method='GET',
            organization='CNSS Central',
            created_by=admin.id,
            is_public=True,
            auth_required=True
        )

        api2 = APIEndpoint(
            name='Salary Information API',
            description='Get salary and contribution information',
            endpoint_url='https://jsonplaceholder.typicode.com/posts',
            method='GET',
            organization='CNSS Central',
            created_by=admin.id,
            is_public=True,
            auth_required=True
        )

        db.session.add(api1)
        db.session.add(api2)

    if agent:
        # Sample APIs from CNSS Casablanca
        api3 = APIEndpoint(
            name='Local Employee Registry',
            description='Local employee registration and management',
            endpoint_url='https://jsonplaceholder.typicode.com/albums',
            method='GET',
            organization='CNSS Casablanca',
            created_by=agent.id,
            is_public=True,
            auth_required=False
        )

        db.session.add(api3)

    db.session.commit()
    print("Sample API endpoints created")

app = create_app()

@app.shell_context_processor
def make_shell_context():
    """Make database models available in flask shell"""
    return {
        'db': db,
        'User': User,
        'APIEndpoint': APIEndpoint,
        'APICall': APICall,
        'AuditLog': AuditLog
    }

def initialize_database():
    """Initialize database"""
    with app.app_context():
        create_tables()
        create_admin_user()
        create_sample_agent()
        create_sample_apis()

if __name__ == '__main__':
    # Initialize database
    initialize_database()

    # Run the application
    app.run(
        host=os.environ.get('FLASK_HOST', '0.0.0.0'),
        port=int(os.environ.get('FLASK_PORT', 5000)),
        debug=os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    )
