import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { employeurAPI } from '../services/api';
import LoadingSpinner from '../components/LoadingSpinner';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  BuildingOfficeIcon,
} from '@heroicons/react/24/outline';

const Employeurs = () => {
  const [employeurs, setEmployeurs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showModal, setShowModal] = useState(false);
  const [selectedEmployeur, setSelectedEmployeur] = useState(null);
  const [modalMode, setModalMode] = useState('view'); // 'view', 'create', 'edit'

  useEffect(() => {
    fetchEmployeurs();
  }, [currentPage, searchTerm]);

  const fetchEmployeurs = async () => {
    try {
      setLoading(true);
      const response = await employeurAPI.getAll({
        page: currentPage,
        per_page: 20,
        search: searchTerm,
      });
      
      setEmployeurs(response.data.employeurs);
      setTotalPages(response.data.pagination.pages);
    } catch (err) {
      setError(err.response?.data?.message || 'Erreur lors du chargement des employeurs');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleView = (employeur) => {
    setSelectedEmployeur(employeur);
    setModalMode('view');
    setShowModal(true);
  };

  const handleEdit = (employeur) => {
    setSelectedEmployeur(employeur);
    setModalMode('edit');
    setShowModal(true);
  };

  const handleCreate = () => {
    setSelectedEmployeur(null);
    setModalMode('create');
    setShowModal(true);
  };

  const handleDelete = async (employeur) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer l'employeur ${employeur.emp_rais}?`)) {
      try {
        await employeurAPI.delete(employeur.emp_mat);
        fetchEmployeurs();
      } catch (err) {
        alert(err.response?.data?.message || 'Erreur lors de la suppression');
      }
    }
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedEmployeur(null);
  };

  const handleModalSuccess = () => {
    closeModal();
    fetchEmployeurs();
  };

  if (loading && employeurs.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Employeurs</h1>
          <p className="mt-1 text-sm text-secondary-600">
            Gérez les employeurs affiliés au système CNSS
          </p>
        </div>
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={handleCreate}
          className="btn-primary flex items-center"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          Nouvel Employeur
        </motion.button>
      </div>

      {/* Search and Filters */}
      <div className="card">
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-secondary-400" />
            <input
              type="text"
              placeholder="Rechercher par matricule, raison sociale, email..."
              value={searchTerm}
              onChange={handleSearch}
              className="input-field pl-10"
            />
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-error-50 border border-error-200 rounded-lg p-4">
          <p className="text-error-600">{error}</p>
        </div>
      )}

      {/* Table */}
      <div className="card p-0">
        <div className="table-container">
          <table className="min-w-full divide-y divide-secondary-200">
            <thead className="bg-secondary-50">
              <tr>
                <th className="table-header">Matricule</th>
                <th className="table-header">Raison Sociale</th>
                <th className="table-header">Activité</th>
                <th className="table-header">Téléphone</th>
                <th className="table-header">Email</th>
                <th className="table-header">Date Affiliation</th>
                <th className="table-header">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-secondary-200">
              <AnimatePresence>
                {employeurs.map((employeur, index) => (
                  <motion.tr
                    key={`${employeur.emp_mat}-${employeur.emp_cle}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.05 }}
                    className="hover:bg-secondary-50"
                  >
                    <td className="table-cell">
                      <div className="flex items-center">
                        <BuildingOfficeIcon className="h-5 w-5 text-secondary-400 mr-2" />
                        <span className="font-medium">
                          {employeur.emp_mat}-{employeur.emp_cle}
                        </span>
                      </div>
                    </td>
                    <td className="table-cell">
                      <div>
                        <div className="font-medium text-secondary-900">
                          {employeur.emp_rais}
                        </div>
                        {employeur.emp_sigle && (
                          <div className="text-sm text-secondary-500">
                            {employeur.emp_sigle}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="table-cell">
                      <span className="text-sm text-secondary-600">
                        {employeur.emp_activite || '-'}
                      </span>
                    </td>
                    <td className="table-cell">
                      <span className="text-sm text-secondary-600">
                        {employeur.emp_tel || '-'}
                      </span>
                    </td>
                    <td className="table-cell">
                      <span className="text-sm text-secondary-600">
                        {employeur.emp_email || '-'}
                      </span>
                    </td>
                    <td className="table-cell">
                      <span className="text-sm text-secondary-600">
                        {employeur.emp_dtaff ? new Date(employeur.emp_dtaff).toLocaleDateString('fr-FR') : '-'}
                      </span>
                    </td>
                    <td className="table-cell">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleView(employeur)}
                          className="p-1 text-secondary-400 hover:text-primary-600 transition-colors"
                          title="Voir"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleEdit(employeur)}
                          className="p-1 text-secondary-400 hover:text-warning-600 transition-colors"
                          title="Modifier"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(employeur)}
                          className="p-1 text-secondary-400 hover:text-error-600 transition-colors"
                          title="Supprimer"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </AnimatePresence>
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-secondary-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-secondary-600">
                Page {currentPage} sur {totalPages}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Précédent
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Suivant
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Loading overlay */}
      {loading && employeurs.length > 0 && (
        <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center z-50">
          <LoadingSpinner size="large" />
        </div>
      )}

      {/* Modal would go here - will create separate component */}
    </div>
  );
};

export default Employeurs;
