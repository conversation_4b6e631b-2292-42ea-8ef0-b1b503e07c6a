from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import J<PERSON>TManager
from flask_cors import CORS
from flask_migrate import Migrate
from config.config import config
import os

# Initialize extensions
db = SQLAlchemy()
jwt = JWTManager()
migrate = Migrate()

def create_app(config_name=None):
    """Application factory pattern."""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Initialize extensions with app
    db.init_app(app)
    jwt.init_app(app)
    migrate.init_app(app, db)
    
    # Configure CORS
    CORS(app, origins=["http://localhost:3000"], supports_credentials=True)
    
    # Register blueprints
    from app.routes.auth import auth_bp
    from app.routes.employeur import employeur_bp
    from app.routes.assure import assure_bp
    from app.routes.beneficiaire import beneficiaire_bp
    from app.routes.admin import admin_bp
    from app.routes.logs import logs_bp
    
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(employeur_bp, url_prefix='/api/employeur')
    app.register_blueprint(assure_bp, url_prefix='/api/assure')
    app.register_blueprint(beneficiaire_bp, url_prefix='/api/beneficiaire')
    app.register_blueprint(admin_bp, url_prefix='/api/admin')
    app.register_blueprint(logs_bp, url_prefix='/api/logs')
    
    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        return {'error': 'Resource not found'}, 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return {'error': 'Internal server error'}, 500
    
    return app
