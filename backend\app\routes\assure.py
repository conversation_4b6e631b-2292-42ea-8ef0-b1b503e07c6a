from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models.assure import Assure
from app.models.employeur import Employeur
from app.models.user import User
from app.utils.decorators import agent_or_admin_required, audit_log, validate_json
from datetime import datetime
from sqlalchemy import or_

assure_bp = Blueprint('assure', __name__)

@assure_bp.route('/', methods=['GET'])
@agent_or_admin_required
@audit_log('READ', 'assure')
def get_assures():
    """Get all assures with pagination and search"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '', type=str)
    employeur_id = request.args.get('employeur_id', type=int)
    
    # Build query
    query = Assure.query
    
    # Filter by employeur if specified
    if employeur_id:
        query = query.filter_by(employeur_id=employeur_id)
    
    # Apply search filter
    if search:
        query = query.filter(
            or_(
                Assure.numero_assure.ilike(f'%{search}%'),
                Assure.cin.ilike(f'%{search}%'),
                Assure.nom.ilike(f'%{search}%'),
                Assure.prenom.ilike(f'%{search}%'),
                Assure.email.ilike(f'%{search}%')
            )
        )
    
    # Apply pagination
    assures = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'message': 'Assures retrieved successfully',
        'assures': [assure.to_dict() for assure in assures.items],
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': assures.total,
            'pages': assures.pages,
            'has_next': assures.has_next,
            'has_prev': assures.has_prev
        }
    }), 200

@assure_bp.route('/<int:id>', methods=['GET'])
@agent_or_admin_required
@audit_log('READ', 'assure')
def get_assure(id):
    """Get specific assure by ID"""
    assure = Assure.query.get_or_404(id)
    
    return jsonify({
        'message': 'Assure retrieved successfully',
        'assure': assure.to_dict()
    }), 200

@assure_bp.route('/', methods=['POST'])
@agent_or_admin_required
@validate_json('numero_assure', 'nom', 'prenom')
@audit_log('CREATE', 'assure')
def create_assure():
    """Create new assure"""
    data = request.get_json()
    current_user_id = get_jwt_identity()
    
    # Check if numero_assure already exists
    existing = Assure.query.filter_by(numero_assure=data['numero_assure']).first()
    if existing:
        return jsonify({'message': 'Numero assure already exists'}), 400
    
    # Check if CIN already exists (if provided)
    if data.get('cin'):
        existing_cin = Assure.query.filter_by(cin=data['cin']).first()
        if existing_cin:
            return jsonify({'message': 'CIN already exists'}), 400
    
    # Validate employeur if provided
    employeur = None
    if data.get('employeur_id'):
        employeur = Employeur.query.get(data['employeur_id'])
        if not employeur:
            return jsonify({'message': 'Employeur not found'}), 400
    
    # Create new assure
    assure = Assure(
        numero_assure=data['numero_assure'],
        cin=data.get('cin'),
        nom=data['nom'],
        prenom=data['prenom'],
        nom_jeune_fille=data.get('nom_jeune_fille'),
        date_naissance=datetime.strptime(data['date_naissance'], '%Y-%m-%d').date() if data.get('date_naissance') else None,
        lieu_naissance=data.get('lieu_naissance'),
        sexe=data.get('sexe'),
        nationalite=data.get('nationalite'),
        situation_familiale=data.get('situation_familiale'),
        adresse=data.get('adresse'),
        ville=data.get('ville'),
        code_postal=data.get('code_postal'),
        telephone=data.get('telephone'),
        email=data.get('email'),
        employeur_id=data.get('employeur_id'),
        numero_employeur=employeur.numero_employeur if employeur else data.get('numero_employeur'),
        date_affiliation=datetime.strptime(data['date_affiliation'], '%Y-%m-%d').date() if data.get('date_affiliation') else None,
        statut=data.get('statut', 'actif'),
        profession=data.get('profession'),
        qualification=data.get('qualification'),
        salaire_base=data.get('salaire_base'),
        created_by=current_user_id,
        updated_by=current_user_id
    )
    
    db.session.add(assure)
    db.session.commit()
    
    return jsonify({
        'message': 'Assure created successfully',
        'assure': assure.to_dict()
    }), 201

@assure_bp.route('/<int:id>', methods=['PUT'])
@agent_or_admin_required
@validate_json('nom', 'prenom')
@audit_log('UPDATE', 'assure')
def update_assure(id):
    """Update existing assure"""
    assure = Assure.query.get_or_404(id)
    data = request.get_json()
    current_user_id = get_jwt_identity()
    
    # Check if numero_assure is being changed and if it already exists
    if 'numero_assure' in data and data['numero_assure'] != assure.numero_assure:
        existing = Assure.query.filter_by(numero_assure=data['numero_assure']).first()
        if existing:
            return jsonify({'message': 'Numero assure already exists'}), 400
        assure.numero_assure = data['numero_assure']
    
    # Check if CIN is being changed and if it already exists
    if 'cin' in data and data['cin'] != assure.cin:
        if data['cin']:  # Only check if CIN is not empty
            existing_cin = Assure.query.filter_by(cin=data['cin']).first()
            if existing_cin:
                return jsonify({'message': 'CIN already exists'}), 400
        assure.cin = data['cin']
    
    # Validate employeur if being changed
    if 'employeur_id' in data and data['employeur_id'] != assure.employeur_id:
        if data['employeur_id']:
            employeur = Employeur.query.get(data['employeur_id'])
            if not employeur:
                return jsonify({'message': 'Employeur not found'}), 400
            assure.employeur_id = data['employeur_id']
            assure.numero_employeur = employeur.numero_employeur
        else:
            assure.employeur_id = None
            assure.numero_employeur = data.get('numero_employeur')
    
    # Update fields
    assure.nom = data.get('nom', assure.nom)
    assure.prenom = data.get('prenom', assure.prenom)
    assure.nom_jeune_fille = data.get('nom_jeune_fille', assure.nom_jeune_fille)
    assure.lieu_naissance = data.get('lieu_naissance', assure.lieu_naissance)
    assure.sexe = data.get('sexe', assure.sexe)
    assure.nationalite = data.get('nationalite', assure.nationalite)
    assure.situation_familiale = data.get('situation_familiale', assure.situation_familiale)
    assure.adresse = data.get('adresse', assure.adresse)
    assure.ville = data.get('ville', assure.ville)
    assure.code_postal = data.get('code_postal', assure.code_postal)
    assure.telephone = data.get('telephone', assure.telephone)
    assure.email = data.get('email', assure.email)
    assure.statut = data.get('statut', assure.statut)
    assure.profession = data.get('profession', assure.profession)
    assure.qualification = data.get('qualification', assure.qualification)
    assure.salaire_base = data.get('salaire_base', assure.salaire_base)
    
    # Update dates
    if data.get('date_naissance'):
        assure.date_naissance = datetime.strptime(data['date_naissance'], '%Y-%m-%d').date()
    if data.get('date_affiliation'):
        assure.date_affiliation = datetime.strptime(data['date_affiliation'], '%Y-%m-%d').date()
    if data.get('date_radiation'):
        assure.date_radiation = datetime.strptime(data['date_radiation'], '%Y-%m-%d').date()
    
    assure.updated_by = current_user_id
    assure.updated_at = datetime.utcnow()
    
    db.session.commit()
    
    return jsonify({
        'message': 'Assure updated successfully',
        'assure': assure.to_dict()
    }), 200

@assure_bp.route('/<int:id>', methods=['DELETE'])
@agent_or_admin_required
@audit_log('DELETE', 'assure')
def delete_assure(id):
    """Delete assure"""
    assure = Assure.query.get_or_404(id)
    
    # Check if assure has associated beneficiaires
    if assure.beneficiaires:
        return jsonify({
            'message': 'Cannot delete assure with associated beneficiaires'
        }), 400
    
    db.session.delete(assure)
    db.session.commit()
    
    return jsonify({'message': 'Assure deleted successfully'}), 200

@assure_bp.route('/stats', methods=['GET'])
@agent_or_admin_required
@audit_log('READ', 'assure')
def get_assure_stats():
    """Get assure statistics"""
    total_assures = Assure.query.count()
    active_assures = Assure.query.filter_by(statut='actif').count()
    inactive_assures = total_assures - active_assures
    
    # Get assures by gender
    male_count = Assure.query.filter_by(sexe='M').count()
    female_count = Assure.query.filter_by(sexe='F').count()
    
    return jsonify({
        'message': 'Assure statistics retrieved successfully',
        'stats': {
            'total': total_assures,
            'active': active_assures,
            'inactive': inactive_assures,
            'by_gender': {
                'male': male_count,
                'female': female_count
            }
        }
    }), 200
