from app import db
from datetime import datetime

class Beneficiaire(db.Model):
    """
    Model for the existing 'beneficiaire' table in Oracle database.
    Based on the actual Oracle schema provided.
    """
    __tablename__ = 'beneficiaire'

    # Primary key
    ben_iducnss = db.Column(db.Numeric(8), primary_key=True)

    # Location codes
    loc_codpos = db.Column(db.Numeric(4))
    loc_indice = db.Column(db.Numeric(1))

    # Assure reference
    ass_mat = db.Column(db.Numeric(8))
    ass_cle = db.Column(db.Numeric(2))

    # Administrative codes
    pay_cod = db.Column(db.Numeric(4))
    ben_type = db.Column(db.Numeric(1))  # Type bénéficiaire
    ben_rang = db.Column(db.Numeric(2))  # Rang
    ben_iu = db.Column(db.Numeric(10))  # Identifiant unique

    # Personal information - French
    ben_nom = db.Column(db.String(30))  # Nom
    ben_prenom = db.Column(db.String(30))  # Prénom
    ben_prnper = db.Column(db.String(30))  # Prénom père
    ben_prngp = db.Column(db.String(30))  # Prénom grand-père
    ben_prnmer = db.Column(db.String(30))  # Prénom mère
    ben_nommer = db.Column(db.String(30))  # Nom mère

    # Personal information - Arabic
    ben_nom_ar = db.Column(db.String(30))  # Nom arabe
    ben_prn_ar = db.Column(db.String(30))  # Prénom arabe
    ben_prnper_ar = db.Column(db.String(30))  # Prénom père arabe
    ben_prngp_ar = db.Column(db.String(30))  # Prénom grand-père arabe
    ben_prnmer_ar = db.Column(db.String(30))  # Prénom mère arabe
    ben_nommer_ar = db.Column(db.String(30))  # Nom mère arabe

    # Personal details
    ben_sexe = db.Column(db.Numeric(1))  # Sexe (0: F, 1: M)
    ben_sitfam = db.Column(db.Numeric(1))  # Situation familiale
    ben_dtnais = db.Column(db.Date)  # Date naissance
    ben_lnais = db.Column(db.String(30))  # Lieu naissance
    ben_lnais_ar = db.Column(db.String(30))  # Lieu naissance arabe
    ben_dtsitfam = db.Column(db.Date)  # Date situation familiale

    # Identification
    ben_typid = db.Column(db.Numeric(1))  # Type ID (0: CIN, 1: Passeport, 2: Carte séjour)
    ben_numid = db.Column(db.String(15))  # Numéro ID
    ben_dtid = db.Column(db.Date)  # Date ID

    # Address
    ben_adr = db.Column(db.String(120))  # Adresse
    ben_adr_ar = db.Column(db.String(120))  # Adresse arabe
    ben_codpiece = db.Column(db.String(1))  # Code pièce

    # Nationality and location
    ben_nactn = db.Column(db.Numeric(6))  # Nationalité code
    ben_aactn = db.Column(db.Numeric(4))  # Année action
    ben_lactn = db.Column(db.String(20))  # Lieu action

    # Rights and benefits
    ben_draf = db.Column(db.Numeric(1))  # Droit affiliation
    ben_dtdaf = db.Column(db.Date)  # Date début affiliation
    ben_dtfaf = db.Column(db.Date)  # Date fin affiliation
    ben_dsoin = db.Column(db.Numeric(1))  # Droit soins
    ben_dtdsoin = db.Column(db.Date)  # Date début soins
    ben_dtfsoin = db.Column(db.Date)  # Date fin soins

    # Administrative fields
    ben_flper = db.Column(db.Numeric(1))  # Flag période
    ben_lpar = db.Column(db.Numeric(1))  # Lien parenté
    ben_agent = db.Column(db.Numeric(6))  # Agent
    etr_mat = db.Column(db.Numeric(19))  # Matricule étranger
    ben_imadat = db.Column(db.Numeric(5))  # Immatriculation date
    ben_consulat = db.Column(db.Numeric(1))  # Consulat
    ben_arrondis = db.Column(db.Numeric(2))  # Arrondissement
    ben_annreg = db.Column(db.Numeric(4))  # Année régime
    ben_numact = db.Column(db.Numeric(6))  # Numéro acte
    ben_municip = db.Column(db.Numeric(5))  # Municipalité
    ben_validact = db.Column(db.Numeric(1))  # Validation acte

    # Geographic codes
    arrondissement_gvt_cod = db.Column(db.Numeric(2))  # Arrondissement gouvernement
    arrondissement_dlg_cod = db.Column(db.Numeric(2))  # Arrondissement délégation
    arrondissement_mun_code = db.Column(db.Numeric(4))  # Arrondissement municipalité
    arrondissement_aro_code = db.Column(db.Numeric(4))  # Arrondissement arrondissement

    # Standardized names
    pre_fra_std = db.Column(db.String(250))  # Prénom français standardisé
    nom_fra_std = db.Column(db.String(250))  # Nom français standardisé

    # Additional fields
    ben_rc = db.Column(db.Numeric(12))  # RC
    ben_tel = db.Column(db.Numeric(8))  # Téléphone
    ben_email = db.Column(db.String(30))  # Email
    ben_identitemadania = db.Column(db.String(500))  # Identité madania
    ben_identitemeremadania = db.Column(db.String(500))  # Identité mère madania
    ben_dtsaisie = db.Column(db.Date)  # Date saisie
    ben_dtmaj = db.Column(db.Date)  # Date mise à jour

    # Audit fields (added for application use)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    updated_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # Relationships
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_beneficiaires')
    updater = db.relationship('User', foreign_keys=[updated_by], backref='updated_beneficiaires')

    def to_dict(self):
        """Convert beneficiaire to dictionary"""
        return {
            'ben_iducnss': int(self.ben_iducnss) if self.ben_iducnss else None,
            'ass_mat': int(self.ass_mat) if self.ass_mat else None,
            'ass_cle': int(self.ass_cle) if self.ass_cle else None,
            'ben_iu': int(self.ben_iu) if self.ben_iu else None,
            'ben_nom': self.ben_nom,
            'ben_nom_ar': self.ben_nom_ar,
            'ben_prenom': self.ben_prenom,
            'ben_prn_ar': self.ben_prn_ar,
            'ben_prnper': self.ben_prnper,
            'ben_prnmer': self.ben_prnmer,
            'ben_nommer': self.ben_nommer,
            'ben_sexe': int(self.ben_sexe) if self.ben_sexe else None,
            'ben_sitfam': int(self.ben_sitfam) if self.ben_sitfam else None,
            'ben_dtnais': self.ben_dtnais.isoformat() if self.ben_dtnais else None,
            'ben_lnais': self.ben_lnais,
            'ben_lnais_ar': self.ben_lnais_ar,
            'ben_typid': int(self.ben_typid) if self.ben_typid else None,
            'ben_numid': self.ben_numid,
            'ben_dtid': self.ben_dtid.isoformat() if self.ben_dtid else None,
            'ben_adr': self.ben_adr,
            'ben_adr_ar': self.ben_adr_ar,
            'ben_dtdaf': self.ben_dtdaf.isoformat() if self.ben_dtdaf else None,
            'ben_dtfaf': self.ben_dtfaf.isoformat() if self.ben_dtfaf else None,
            'ben_dtdsoin': self.ben_dtdsoin.isoformat() if self.ben_dtdsoin else None,
            'ben_dtfsoin': self.ben_dtfsoin.isoformat() if self.ben_dtfsoin else None,
            'ben_tel': int(self.ben_tel) if self.ben_tel else None,
            'ben_email': self.ben_email,
            'ben_rc': int(self.ben_rc) if self.ben_rc else None,
            'ben_dtsaisie': self.ben_dtsaisie.isoformat() if self.ben_dtsaisie else None,
            'ben_dtmaj': self.ben_dtmaj.isoformat() if self.ben_dtmaj else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def __repr__(self):
        return f'<Beneficiaire {self.ben_iducnss}: {self.ben_nom} {self.ben_prenom}>'
