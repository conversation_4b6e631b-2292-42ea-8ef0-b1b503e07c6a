from app import db
from datetime import datetime

class Beneficiaire(db.Model):
    """
    Model for the existing 'beneficiaire' table in Oracle database.
    This reflects the existing table structure - modify columns as needed
    based on your actual Oracle table schema.
    """
    __tablename__ = 'beneficiaire'
    
    # Primary key - adjust based on your actual table
    id = db.Column(db.Integer, primary_key=True)
    
    # Personal identification
    numero_beneficiaire = db.Column(db.String(50), unique=True, nullable=False)
    cin = db.Column(db.String(20))
    nom = db.Column(db.String(100), nullable=False)
    prenom = db.Column(db.String(100), nullable=False)
    nom_jeune_fille = db.Column(db.String(100))
    
    # Personal details
    date_naissance = db.Column(db.Date)
    lieu_naissance = db.Column(db.String(100))
    sexe = db.Column(db.String(1))  # M/F
    nationalite = db.Column(db.String(50))
    
    # Contact information
    adresse = db.Column(db.Text)
    ville = db.Column(db.String(100))
    code_postal = db.Column(db.String(20))
    telephone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    
    # Relationship to assure
    assure_id = db.Column(db.Integer, db.ForeignKey('assure.id'))
    numero_assure = db.Column(db.String(50))
    lien_parente = db.Column(db.String(50))  # conjoint, enfant, parent, etc.
    
    # Benefit information
    type_beneficiaire = db.Column(db.String(50))  # ayant_droit, beneficiaire_pension, etc.
    date_ouverture_droits = db.Column(db.Date)
    date_fermeture_droits = db.Column(db.Date)
    statut = db.Column(db.String(50), default='actif')
    
    # Medical information (if applicable)
    numero_carte_soins = db.Column(db.String(50))
    date_emission_carte = db.Column(db.Date)
    date_expiration_carte = db.Column(db.Date)
    
    # Audit fields
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    updated_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # Relationships
    assure = db.relationship('Assure', backref='beneficiaires')
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_beneficiaires')
    updater = db.relationship('User', foreign_keys=[updated_by], backref='updated_beneficiaires')
    
    def to_dict(self):
        """Convert beneficiaire to dictionary"""
        return {
            'id': self.id,
            'numero_beneficiaire': self.numero_beneficiaire,
            'cin': self.cin,
            'nom': self.nom,
            'prenom': self.prenom,
            'nom_jeune_fille': self.nom_jeune_fille,
            'date_naissance': self.date_naissance.isoformat() if self.date_naissance else None,
            'lieu_naissance': self.lieu_naissance,
            'sexe': self.sexe,
            'nationalite': self.nationalite,
            'adresse': self.adresse,
            'ville': self.ville,
            'code_postal': self.code_postal,
            'telephone': self.telephone,
            'email': self.email,
            'numero_assure': self.numero_assure,
            'lien_parente': self.lien_parente,
            'type_beneficiaire': self.type_beneficiaire,
            'date_ouverture_droits': self.date_ouverture_droits.isoformat() if self.date_ouverture_droits else None,
            'date_fermeture_droits': self.date_fermeture_droits.isoformat() if self.date_fermeture_droits else None,
            'statut': self.statut,
            'numero_carte_soins': self.numero_carte_soins,
            'date_emission_carte': self.date_emission_carte.isoformat() if self.date_emission_carte else None,
            'date_expiration_carte': self.date_expiration_carte.isoformat() if self.date_expiration_carte else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f'<Beneficiaire {self.numero_beneficiaire}: {self.nom} {self.prenom}>'
