version: '3.8'

services:
  # Oracle Database (using Oracle XE)
  oracle-db:
    image: container-registry.oracle.com/database/express:21.3.0-xe
    container_name: cnss_oracle_db
    environment:
      - ORACLE_PWD=OraclePassword123
      - ORACLE_CHARACTERSET=AL32UTF8
    ports:
      - "1521:1521"
      - "5500:5500"
    volumes:
      - oracle_data:/opt/oracle/oradata
      - ./database/init:/opt/oracle/scripts/startup
    networks:
      - cnss_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "sqlplus", "-L", "system/OraclePassword123@//localhost:1521/XE", "@/dev/null"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Flask Backend API
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: cnss_backend
    environment:
      - FLASK_ENV=production
      - FLASK_DEBUG=False
      - SECRET_KEY=cnss-production-secret-key-change-me
      - JWT_SECRET_KEY=cnss-jwt-secret-key-change-me
      - ORACLE_USER=cnss_user
      - ORACLE_PASSWORD=cnss_password
      - ORACLE_HOST=oracle-db
      - ORACLE_PORT=1521
      - ORACLE_SERVICE=XEPDB1
      - CORS_ORIGINS=http://localhost:3000,http://frontend:3000
    ports:
      - "5000:5000"
    depends_on:
      oracle-db:
        condition: service_healthy
    networks:
      - cnss_network
    restart: unless-stopped
    volumes:
      - ./backend/logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/admin/system/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # React Frontend
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: cnss_frontend
    environment:
      - REACT_APP_API_URL=http://localhost:5000/api
      - REACT_APP_ENV=production
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - cnss_network
    restart: unless-stopped
    volumes:
      - ./src:/app/src
      - ./public:/app/public
      - /app/node_modules

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: cnss_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - cnss_network
    restart: unless-stopped

volumes:
  oracle_data:
    driver: local

networks:
  cnss_network:
    driver: bridge
