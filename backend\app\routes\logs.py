from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models.audit_log import AuditLog
from app.models.user import User
from app.utils.decorators import admin_required, audit_log
from datetime import datetime, timedelta
from sqlalchemy import or_, func

logs_bp = Blueprint('logs', __name__)

@logs_bp.route('/', methods=['GET'])
@admin_required
@audit_log('READ', 'audit_log')
def get_audit_logs():
    """Get audit logs with pagination and filtering"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    search = request.args.get('search', '', type=str)
    action = request.args.get('action', '', type=str)
    resource_type = request.args.get('resource_type', '', type=str)
    user_id = request.args.get('user_id', type=int)
    start_date = request.args.get('start_date', '', type=str)
    end_date = request.args.get('end_date', '', type=str)
    
    # Build query
    query = AuditLog.query
    
    # Apply filters
    if action:
        query = query.filter_by(action=action)
    
    if resource_type:
        query = query.filter_by(resource_type=resource_type)
    
    if user_id:
        query = query.filter_by(user_id=user_id)
    
    # Date range filter
    if start_date:
        try:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            query = query.filter(AuditLog.timestamp >= start_dt)
        except ValueError:
            pass
    
    if end_date:
        try:
            end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
            query = query.filter(AuditLog.timestamp < end_dt)
        except ValueError:
            pass
    
    # Apply search filter
    if search:
        query = query.filter(
            or_(
                AuditLog.username.ilike(f'%{search}%'),
                AuditLog.endpoint.ilike(f'%{search}%'),
                AuditLog.ip_address.ilike(f'%{search}%'),
                AuditLog.response_message.ilike(f'%{search}%')
            )
        )
    
    # Order by timestamp descending
    query = query.order_by(AuditLog.timestamp.desc())
    
    # Apply pagination
    logs = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'message': 'Audit logs retrieved successfully',
        'logs': [log.to_dict() for log in logs.items],
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': logs.total,
            'pages': logs.pages,
            'has_next': logs.has_next,
            'has_prev': logs.has_prev
        }
    }), 200

@logs_bp.route('/<int:id>', methods=['GET'])
@admin_required
@audit_log('READ', 'audit_log')
def get_audit_log(id):
    """Get specific audit log by ID"""
    log = AuditLog.query.get_or_404(id)
    
    return jsonify({
        'message': 'Audit log retrieved successfully',
        'log': log.to_dict()
    }), 200

@logs_bp.route('/stats', methods=['GET'])
@admin_required
@audit_log('READ', 'audit_log')
def get_log_stats():
    """Get audit log statistics"""
    # Time range for statistics (default: last 30 days)
    days = request.args.get('days', 30, type=int)
    start_date = datetime.utcnow() - timedelta(days=days)
    
    # Total logs in time range
    total_logs = AuditLog.query.filter(AuditLog.timestamp >= start_date).count()
    
    # Logs by action
    action_stats = db.session.query(
        AuditLog.action,
        func.count(AuditLog.id).label('count')
    ).filter(AuditLog.timestamp >= start_date).group_by(AuditLog.action).all()
    
    # Logs by resource type
    resource_stats = db.session.query(
        AuditLog.resource_type,
        func.count(AuditLog.id).label('count')
    ).filter(AuditLog.timestamp >= start_date).group_by(AuditLog.resource_type).all()
    
    # Logs by user
    user_stats = db.session.query(
        AuditLog.username,
        func.count(AuditLog.id).label('count')
    ).filter(AuditLog.timestamp >= start_date).group_by(AuditLog.username).order_by(func.count(AuditLog.id).desc()).limit(10).all()
    
    # Logs by status code
    status_stats = db.session.query(
        AuditLog.status_code,
        func.count(AuditLog.id).label('count')
    ).filter(AuditLog.timestamp >= start_date).group_by(AuditLog.status_code).all()
    
    # Daily activity (last 7 days)
    daily_stats = []
    for i in range(7):
        day_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=i)
        day_end = day_start + timedelta(days=1)
        count = AuditLog.query.filter(
            AuditLog.timestamp >= day_start,
            AuditLog.timestamp < day_end
        ).count()
        daily_stats.append({
            'date': day_start.strftime('%Y-%m-%d'),
            'count': count
        })
    
    return jsonify({
        'message': 'Audit log statistics retrieved successfully',
        'stats': {
            'total_logs': total_logs,
            'time_range_days': days,
            'by_action': [{'action': a[0], 'count': a[1]} for a in action_stats],
            'by_resource': [{'resource': r[0], 'count': r[1]} for r in resource_stats if r[0]],
            'by_user': [{'username': u[0], 'count': u[1]} for u in user_stats if u[0]],
            'by_status': [{'status_code': s[0], 'count': s[1]} for s in status_stats if s[0]],
            'daily_activity': list(reversed(daily_stats))
        }
    }), 200

@logs_bp.route('/export', methods=['GET'])
@admin_required
@audit_log('EXPORT', 'audit_log')
def export_logs():
    """Export audit logs as CSV"""
    import csv
    import io
    from flask import make_response
    
    # Get filters from query parameters
    start_date = request.args.get('start_date', '', type=str)
    end_date = request.args.get('end_date', '', type=str)
    action = request.args.get('action', '', type=str)
    resource_type = request.args.get('resource_type', '', type=str)
    
    # Build query
    query = AuditLog.query
    
    if action:
        query = query.filter_by(action=action)
    if resource_type:
        query = query.filter_by(resource_type=resource_type)
    
    if start_date:
        try:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            query = query.filter(AuditLog.timestamp >= start_dt)
        except ValueError:
            pass
    
    if end_date:
        try:
            end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
            query = query.filter(AuditLog.timestamp < end_dt)
        except ValueError:
            pass
    
    # Order by timestamp
    logs = query.order_by(AuditLog.timestamp.desc()).limit(10000).all()  # Limit to prevent memory issues
    
    # Create CSV
    output = io.StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow([
        'ID', 'Timestamp', 'Username', 'Role', 'Action', 'Resource Type',
        'Resource ID', 'Endpoint', 'Method', 'IP Address', 'Status Code',
        'Response Message', 'Duration (ms)', 'Organization'
    ])
    
    # Write data
    for log in logs:
        writer.writerow([
            log.id,
            log.timestamp.isoformat() if log.timestamp else '',
            log.username or '',
            log.user_role or '',
            log.action or '',
            log.resource_type or '',
            log.resource_id or '',
            log.endpoint or '',
            log.method or '',
            log.ip_address or '',
            log.status_code or '',
            log.response_message or '',
            log.duration_ms or '',
            log.organization or ''
        ])
    
    # Create response
    output.seek(0)
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = f'attachment; filename=audit_logs_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
    
    return response

@logs_bp.route('/actions', methods=['GET'])
@admin_required
def get_available_actions():
    """Get list of available actions for filtering"""
    actions = db.session.query(AuditLog.action).distinct().all()
    return jsonify({
        'message': 'Available actions retrieved successfully',
        'actions': [action[0] for action in actions if action[0]]
    }), 200

@logs_bp.route('/resources', methods=['GET'])
@admin_required
def get_available_resources():
    """Get list of available resource types for filtering"""
    resources = db.session.query(AuditLog.resource_type).distinct().all()
    return jsonify({
        'message': 'Available resource types retrieved successfully',
        'resource_types': [resource[0] for resource in resources if resource[0]]
    }), 200
