from functools import wraps
from flask import request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from app.models.user import User
from app.models.audit_log import AuditLog
import time

def role_required(*roles):
    """
    Decorator to require specific roles for accessing endpoints
    Usage: @role_required('admin') or @role_required('admin', 'agent')
    """
    def decorator(f):
        @wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            current_user_id = get_jwt_identity()
            user = User.query.get(current_user_id)
            
            if not user or not user.is_active:
                return jsonify({'message': 'User not found or inactive'}), 401
            
            if user.role not in roles:
                return jsonify({'message': 'Insufficient permissions'}), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def admin_required(f):
    """Decorator to require admin role"""
    return role_required('admin')(f)

def agent_or_admin_required(f):
    """Decorator to require agent or admin role"""
    return role_required('agent', 'admin')(f)

def audit_log(action, resource_type=None):
    """
    Decorator to automatically log API actions
    Usage: @audit_log('CREATE', 'employeur')
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            start_time = time.time()
            
            # Get user info
            user_id = None
            try:
                user_id = get_jwt_identity()
            except:
                pass
            
            # Get request info
            ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
            user_agent = request.headers.get('User-Agent')
            endpoint = request.endpoint
            method = request.method
            
            # Get request data (for POST/PUT requests)
            request_data = None
            if method in ['POST', 'PUT', 'PATCH']:
                try:
                    request_data = request.get_json()
                except:
                    pass
            
            # Execute the function
            try:
                result = f(*args, **kwargs)
                
                # Calculate duration
                duration_ms = int((time.time() - start_time) * 1000)
                
                # Determine status code and resource ID
                status_code = 200
                resource_id = None
                response_message = "Success"
                
                if isinstance(result, tuple):
                    if len(result) >= 2:
                        status_code = result[1]
                    if len(result) >= 1 and isinstance(result[0], dict):
                        response_data = result[0]
                        if 'id' in response_data:
                            resource_id = response_data['id']
                        if 'message' in response_data:
                            response_message = response_data['message']
                
                # Extract resource ID from URL parameters if not found in response
                if not resource_id and 'id' in kwargs:
                    resource_id = kwargs['id']
                
                # Log the action
                AuditLog.log_action(
                    user_id=user_id,
                    action=action,
                    resource_type=resource_type,
                    resource_id=resource_id,
                    endpoint=endpoint,
                    method=method,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    request_data=request_data,
                    status_code=status_code,
                    response_message=response_message,
                    duration_ms=duration_ms
                )
                
                return result
                
            except Exception as e:
                # Calculate duration
                duration_ms = int((time.time() - start_time) * 1000)
                
                # Log the error
                AuditLog.log_action(
                    user_id=user_id,
                    action=action,
                    resource_type=resource_type,
                    endpoint=endpoint,
                    method=method,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    request_data=request_data,
                    status_code=500,
                    response_message=str(e),
                    duration_ms=duration_ms
                )
                
                raise e
                
        return decorated_function
    return decorator

def validate_json(*required_fields):
    """
    Decorator to validate JSON request data
    Usage: @validate_json('name', 'email')
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not request.is_json:
                return jsonify({'message': 'Request must be JSON'}), 400
            
            data = request.get_json()
            if not data:
                return jsonify({'message': 'No JSON data provided'}), 400
            
            missing_fields = []
            for field in required_fields:
                if field not in data or data[field] is None or data[field] == '':
                    missing_fields.append(field)
            
            if missing_fields:
                return jsonify({
                    'message': f'Missing required fields: {", ".join(missing_fields)}'
                }), 400
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator
