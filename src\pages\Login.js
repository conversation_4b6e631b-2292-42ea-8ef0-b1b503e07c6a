import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from '../components/LoadingSpinner';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';

const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const { login, isAuthenticated, isLoading, error, clearError } = useAuth();
  const location = useLocation();
  
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm();

  // Clear error when component mounts
  useEffect(() => {
    clearError();
  }, [clearError]);

  // Redirect if already authenticated
  if (isAuthenticated) {
    const from = location.state?.from?.pathname || '/dashboard';
    return <Navigate to={from} replace />;
  }

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  const onSubmit = async (data) => {
    const result = await login(data);
    if (!result.success) {
      // Error is handled by the auth context
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-100 py-12 px-4 sm:px-6 lg:px-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md w-full space-y-8"
      >
        <div>
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
            className="mx-auto h-16 w-16 bg-primary-600 rounded-xl flex items-center justify-center shadow-lg"
          >
            <span className="text-white font-bold text-xl">CNSS</span>
          </motion.div>
          <h2 className="mt-6 text-center text-3xl font-bold text-secondary-900">
            Connexion à votre compte
          </h2>
          <p className="mt-2 text-center text-sm text-secondary-600">
            Système de gestion des données CNSS
          </p>
        </div>

        <motion.form
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="mt-8 space-y-6"
          onSubmit={handleSubmit(onSubmit)}
        >
          <div className="card">
            <div className="space-y-4">
              {/* Username field */}
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-secondary-700">
                  Nom d'utilisateur
                </label>
                <div className="mt-1">
                  <input
                    {...register('username', {
                      required: 'Le nom d\'utilisateur est requis',
                    })}
                    type="text"
                    autoComplete="username"
                    className={`input-field ${errors.username ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''}`}
                    placeholder="Entrez votre nom d'utilisateur"
                  />
                  {errors.username && (
                    <p className="mt-1 text-sm text-error-600">{errors.username.message}</p>
                  )}
                </div>
              </div>

              {/* Password field */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-secondary-700">
                  Mot de passe
                </label>
                <div className="mt-1 relative">
                  <input
                    {...register('password', {
                      required: 'Le mot de passe est requis',
                    })}
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="current-password"
                    className={`input-field pr-10 ${errors.password ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''}`}
                    placeholder="Entrez votre mot de passe"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="h-5 w-5 text-secondary-400" />
                    ) : (
                      <EyeIcon className="h-5 w-5 text-secondary-400" />
                    )}
                  </button>
                  {errors.password && (
                    <p className="mt-1 text-sm text-error-600">{errors.password.message}</p>
                  )}
                </div>
              </div>

              {/* Error message */}
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-error-50 border border-error-200 rounded-lg p-3"
                >
                  <p className="text-sm text-error-600">{error}</p>
                </motion.div>
              )}

              {/* Submit button */}
              <div>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full btn-primary flex justify-center items-center disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <LoadingSpinner size="small" className="mr-2" />
                      Connexion en cours...
                    </>
                  ) : (
                    'Se connecter'
                  )}
                </motion.button>
              </div>
            </div>
          </div>

          {/* Demo credentials */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="bg-primary-50 border border-primary-200 rounded-lg p-4"
          >
            <h3 className="text-sm font-medium text-primary-800 mb-2">
              Comptes de démonstration :
            </h3>
            <div className="text-xs text-primary-700 space-y-1">
              <p><strong>Admin:</strong> admin / admin123</p>
              <p><strong>Agent:</strong> agent1 / agent123</p>
            </div>
          </motion.div>
        </motion.form>
      </motion.div>
    </div>
  );
};

export default Login;
