#!/usr/bin/env python3
"""
Test script to check if backend is running and test login
"""

import requests
import json

def test_backend():
    """Test if backend is running and login works"""
    try:
        # Test if backend is running
        print("Testing backend connection...")
        response = requests.get("http://localhost:5000/api/auth/profile", timeout=5)
        print(f"Backend connection test: {response.status_code}")
        
        # Test login
        print("\nTesting login...")
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        response = requests.post(
            "http://localhost:5000/api/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"Login response status: {response.status_code}")
        print(f"Login response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Login successful!")
            print(f"User: {data.get('user', {}).get('username')}")
            print(f"Role: {data.get('user', {}).get('role')}")
            return True
        else:
            print("❌ Login failed!")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Backend is not running or not accessible on port 5000")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_backend()
