import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { logsAPI } from "../services/api";
import LoadingSpinner from "../components/LoadingSpinner";
import {
  MagnifyingGlassIcon,
  DocumentTextIcon,
  ArrowDownTrayIcon,
  FunnelIcon,
} from "@heroicons/react/24/outline";

const Logs = () => {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    action: "",
    resource_type: "",
    start_date: "",
    end_date: "",
  });
  const [availableActions, setAvailableActions] = useState([]);
  const [availableResources, setAvailableResources] = useState([]);

  const fetchLogs = useCallback(async () => {
    try {
      setLoading(true);
      const response = await logsAPI.getAll({
        page: currentPage,
        per_page: 50,
        search: searchTerm,
        ...filters,
      });

      setLogs(response.data.logs);
      setTotalPages(response.data.pagination.pages);
    } catch (err) {
      setError(
        err.response?.data?.message || "Erreur lors du chargement des logs"
      );
    } finally {
      setLoading(false);
    }
  }, [currentPage, searchTerm, filters]);

  const fetchFilterOptions = useCallback(async () => {
    try {
      const [actionsResponse, resourcesResponse] = await Promise.all([
        logsAPI.getActions(),
        logsAPI.getResources(),
      ]);

      setAvailableActions(actionsResponse.data.actions);
      setAvailableResources(resourcesResponse.data.resource_types);
    } catch (err) {
      console.error("Error fetching filter options:", err);
    }
  }, []);

  useEffect(() => {
    fetchLogs();
  }, [fetchLogs]);

  useEffect(() => {
    fetchFilterOptions();
  }, [fetchFilterOptions]);

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleFilterChange = (key, value) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
    setCurrentPage(1);
  };

  const handleExport = async () => {
    try {
      const response = await logsAPI.export({
        search: searchTerm,
        ...filters,
      });

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute(
        "download",
        `audit_logs_${new Date().toISOString().split("T")[0]}.csv`
      );
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      alert(
        "Erreur lors de l'export: " +
          (err.response?.data?.message || err.message)
      );
    }
  };

  const getStatusColor = (statusCode) => {
    if (statusCode >= 200 && statusCode < 300)
      return "text-success-600 bg-success-100";
    if (statusCode >= 400 && statusCode < 500)
      return "text-warning-600 bg-warning-100";
    if (statusCode >= 500) return "text-error-600 bg-error-100";
    return "text-secondary-600 bg-secondary-100";
  };

  if (loading && logs.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">
            Logs d'Audit
          </h1>
          <p className="mt-1 text-sm text-secondary-600">
            Consultez l'historique des actions effectuées dans le système
          </p>
        </div>
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={handleExport}
          className="btn-secondary flex items-center"
        >
          <ArrowDownTrayIcon className="h-5 w-5 mr-2" />
          Exporter CSV
        </motion.button>
      </div>

      {/* Search and Filters */}
      <div className="card space-y-4">
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-secondary-400" />
            <input
              type="text"
              placeholder="Rechercher par utilisateur, endpoint, IP..."
              value={searchTerm}
              onChange={handleSearch}
              className="input-field pl-10"
            />
          </div>
          <FunnelIcon className="h-5 w-5 text-secondary-400" />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <select
            value={filters.action}
            onChange={(e) => handleFilterChange("action", e.target.value)}
            className="input-field"
          >
            <option value="">Toutes les actions</option>
            {availableActions.map((action) => (
              <option key={action} value={action}>
                {action}
              </option>
            ))}
          </select>

          <select
            value={filters.resource_type}
            onChange={(e) =>
              handleFilterChange("resource_type", e.target.value)
            }
            className="input-field"
          >
            <option value="">Toutes les ressources</option>
            {availableResources.map((resource) => (
              <option key={resource} value={resource}>
                {resource}
              </option>
            ))}
          </select>

          <input
            type="date"
            value={filters.start_date}
            onChange={(e) => handleFilterChange("start_date", e.target.value)}
            className="input-field"
            placeholder="Date début"
          />

          <input
            type="date"
            value={filters.end_date}
            onChange={(e) => handleFilterChange("end_date", e.target.value)}
            className="input-field"
            placeholder="Date fin"
          />
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-error-50 border border-error-200 rounded-lg p-4">
          <p className="text-error-600">{error}</p>
        </div>
      )}

      {/* Table */}
      <div className="card p-0">
        <div className="table-container">
          <table className="min-w-full divide-y divide-secondary-200">
            <thead className="bg-secondary-50">
              <tr>
                <th className="table-header">Timestamp</th>
                <th className="table-header">Utilisateur</th>
                <th className="table-header">Action</th>
                <th className="table-header">Ressource</th>
                <th className="table-header">Endpoint</th>
                <th className="table-header">Status</th>
                <th className="table-header">IP</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-secondary-200">
              <AnimatePresence>
                {logs.map((log, index) => (
                  <motion.tr
                    key={log.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.02 }}
                    className="hover:bg-secondary-50"
                  >
                    <td className="table-cell">
                      <div className="flex items-center">
                        <DocumentTextIcon className="h-4 w-4 text-secondary-400 mr-2" />
                        <div>
                          <div className="text-sm font-medium text-secondary-900">
                            {new Date(log.timestamp).toLocaleDateString(
                              "fr-FR"
                            )}
                          </div>
                          <div className="text-xs text-secondary-500">
                            {new Date(log.timestamp).toLocaleTimeString(
                              "fr-FR"
                            )}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="table-cell">
                      <div>
                        <div className="text-sm font-medium text-secondary-900">
                          {log.username || "Anonyme"}
                        </div>
                        <div className="text-xs text-secondary-500">
                          {log.user_role}
                        </div>
                      </div>
                    </td>
                    <td className="table-cell">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                        {log.action}
                      </span>
                    </td>
                    <td className="table-cell">
                      <span className="text-sm text-secondary-600">
                        {log.resource_type || "-"}
                        {log.resource_id && (
                          <span className="text-xs text-secondary-400 block">
                            ID: {log.resource_id}
                          </span>
                        )}
                      </span>
                    </td>
                    <td className="table-cell">
                      <span className="text-xs font-mono text-secondary-600">
                        {log.method} {log.endpoint}
                      </span>
                    </td>
                    <td className="table-cell">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                          log.status_code
                        )}`}
                      >
                        {log.status_code}
                      </span>
                    </td>
                    <td className="table-cell">
                      <span className="text-xs font-mono text-secondary-600">
                        {log.ip_address}
                      </span>
                    </td>
                  </motion.tr>
                ))}
              </AnimatePresence>
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-secondary-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-secondary-600">
                Page {currentPage} sur {totalPages}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Précédent
                </button>
                <button
                  onClick={() =>
                    setCurrentPage(Math.min(totalPages, currentPage + 1))
                  }
                  disabled={currentPage === totalPages}
                  className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Suivant
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Loading overlay */}
      {loading && logs.length > 0 && (
        <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center z-50">
          <LoadingSpinner size="large" />
        </div>
      )}
    </div>
  );
};

export default Logs;
