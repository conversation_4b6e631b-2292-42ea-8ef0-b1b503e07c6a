from app import db
from datetime import datetime

class Employeur(db.Model):
    """
    Model for the existing 'employeur' table in Oracle database.
    Based on the actual Oracle schema provided.
    """
    __tablename__ = 'employeur'

    # Primary key - composite key from Oracle
    emp_mat = db.Column(db.Numeric(8), primary_key=True)
    emp_cle = db.Column(db.Numeric(2), primary_key=True)

    # Administrative codes
    adm_cod = db.Column(db.String(4))
    pat_num = db.Column(db.Numeric(7))
    pat_cle = db.Column(db.String(1))
    atv_cod = db.Column(db.Numeric(6))
    rpl_id = db.Column(db.String(15))

    # Location codes
    loc_codpos = db.Column(db.Numeric(4))
    loc_indice = db.Column(db.Numeric(1))
    bur_cod = db.Column(db.Numeric(3))
    atm_ref = db.Column(db.Numeric(4))
    dct_dtcot = db.Column(db.Numeric(1))
    loc_loc_codpos = db.Column(db.Numeric(4))
    loc_loc_indice = db.Column(db.Numeric(1))
    reg_cod = db.Column(db.Numeric(3))
    pay_cod = db.Column(db.Numeric(4))

    # Dates
    emp_dteff = db.Column(db.Date)  # Date d'effet
    emp_dtassuj = db.Column(db.Date)  # Date d'assujettissement
    emp_dtdact = db.Column(db.Date)  # Date de désactivation
    emp_dtregcp = db.Column(db.Date)  # Date régime complémentaire
    emp_dtaff = db.Column(db.Date)  # Date d'affiliation
    emp_dtmaj = db.Column(db.Date)  # Date de mise à jour
    emp_dtfinregcp = db.Column(db.Date)  # Date fin régime complémentaire

    # Company information
    emp_rais = db.Column(db.String(80))  # Raison sociale
    emp_rais_ar = db.Column(db.String(80))  # Raison sociale en arabe
    emp_tel = db.Column(db.String(10))  # Téléphone
    emp_fax = db.Column(db.String(10))  # Fax
    emp_email = db.Column(db.String(60))  # Email
    emp_sigle = db.Column(db.String(15))  # Sigle
    emp_enseig = db.Column(db.String(10))  # Enseigne
    emp_activite = db.Column(db.String(60))  # Activité

    # Registration and legal info
    emp_nregc = db.Column(db.String(12))  # Numéro registre commerce
    emp_lregc = db.Column(db.String(30))  # Lieu registre commerce (Tribunal)
    emp_estint = db.Column(db.Numeric(15, 3))  # Estimation intérêts
    emp_catprof = db.Column(db.String(1))  # Catégorie professionnelle
    emp_rib = db.Column(db.Numeric(20))  # RIB
    emp_typaffi = db.Column(db.Numeric(1))  # Type d'affiliation

    # Addresses
    emp_adrcor = db.Column(db.String(100))  # Adresse correspondance
    emp_adrcor_ar = db.Column(db.String(100))  # Adresse correspondance arabe
    emp_deradr = db.Column(db.String(100))  # Dernière adresse
    emp_derar_ar = db.Column(db.String(100))  # Dernière adresse arabe

    # Other fields
    emp_exaffmat = db.Column(db.Numeric(8))  # Ex-affiliation matricule
    emp_exaffcle = db.Column(db.Numeric(2))  # Ex-affiliation clé
    emp_numadh = db.Column(db.Numeric(4))  # Numéro adhésion
    emp_cregcmp = db.Column(db.String(1))  # Code régime complémentaire
    emp_inconnu = db.Column(db.Numeric(1))  # Inconnu
    emp_flagper = db.Column(db.Numeric(1))  # Flag période
    emp_capres = db.Column(db.Numeric(2))  # Capital résiduel
    emp_matag = db.Column(db.Numeric(6))  # Matricule agent
    emp_identrepadhregc = db.Column(db.String(100))  # Identité représentant adhésion régime complémentaire
    emp_qualrepadhregc = db.Column(db.String(100))  # Qualité représentant adhésion régime complémentaire

    # Audit fields (added for application use)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    updated_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    # Relationships
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_employeurs')
    updater = db.relationship('User', foreign_keys=[updated_by], backref='updated_employeurs')
    
    def to_dict(self):
        """Convert employeur to dictionary"""
        return {
            'emp_mat': int(self.emp_mat) if self.emp_mat else None,
            'emp_cle': int(self.emp_cle) if self.emp_cle else None,
            'emp_rais': self.emp_rais,
            'emp_rais_ar': self.emp_rais_ar,
            'emp_tel': self.emp_tel,
            'emp_fax': self.emp_fax,
            'emp_email': self.emp_email,
            'emp_sigle': self.emp_sigle,
            'emp_activite': self.emp_activite,
            'emp_dteff': self.emp_dteff.isoformat() if self.emp_dteff else None,
            'emp_dtassuj': self.emp_dtassuj.isoformat() if self.emp_dtassuj else None,
            'emp_dtaff': self.emp_dtaff.isoformat() if self.emp_dtaff else None,
            'emp_adrcor': self.emp_adrcor,
            'emp_adrcor_ar': self.emp_adrcor_ar,
            'emp_nregc': self.emp_nregc,
            'emp_lregc': self.emp_lregc,
            'pat_num': int(self.pat_num) if self.pat_num else None,
            'atv_cod': int(self.atv_cod) if self.atv_cod else None,
            'bur_cod': int(self.bur_cod) if self.bur_cod else None,
            'pay_cod': int(self.pay_cod) if self.pay_cod else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def __repr__(self):
        return f'<Employeur {self.emp_mat}-{self.emp_cle}: {self.emp_rais}>'
