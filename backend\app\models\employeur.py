from app import db
from datetime import datetime

class Employeur(db.Model):
    """
    Model for the existing 'employeur' table in Oracle database.
    This reflects the existing table structure - modify columns as needed
    based on your actual Oracle table schema.
    """
    __tablename__ = 'employeur'
    
    # Primary key - adjust based on your actual table
    id = db.Column(db.Integer, primary_key=True)
    
    # Common employeur fields - adjust these based on your actual Oracle table structure
    numero_employeur = db.Column(db.String(50), unique=True, nullable=False)
    raison_sociale = db.Column(db.String(200), nullable=False)
    forme_juridique = db.Column(db.String(100))
    secteur_activite = db.Column(db.String(100))
    adresse = db.Column(db.Text)
    ville = db.Column(db.String(100))
    code_postal = db.Column(db.String(20))
    telephone = db.Column(db.String(20))
    fax = db.Column(db.String(20))
    email = db.Column(db.String(120))
    
    # Registration and status fields
    date_affiliation = db.Column(db.Date)
    statut = db.Column(db.String(50), default='actif')
    
    # Financial fields
    capital_social = db.Column(db.Numeric(15, 2))
    chiffre_affaires = db.Column(db.Numeric(15, 2))
    
    # Audit fields
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    updated_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # Relationships
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_employeurs')
    updater = db.relationship('User', foreign_keys=[updated_by], backref='updated_employeurs')
    
    def to_dict(self):
        """Convert employeur to dictionary"""
        return {
            'id': self.id,
            'numero_employeur': self.numero_employeur,
            'raison_sociale': self.raison_sociale,
            'forme_juridique': self.forme_juridique,
            'secteur_activite': self.secteur_activite,
            'adresse': self.adresse,
            'ville': self.ville,
            'code_postal': self.code_postal,
            'telephone': self.telephone,
            'fax': self.fax,
            'email': self.email,
            'date_affiliation': self.date_affiliation.isoformat() if self.date_affiliation else None,
            'statut': self.statut,
            'capital_social': float(self.capital_social) if self.capital_social else None,
            'chiffre_affaires': float(self.chiffre_affaires) if self.chiffre_affaires else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f'<Employeur {self.numero_employeur}: {self.raison_sociale}>'
