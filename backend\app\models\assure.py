from app import db
from datetime import datetime

class Assure(db.Model):
    """
    Model for the existing 'assure' table in Oracle database.
    This reflects the existing table structure - modify columns as needed
    based on your actual Oracle table schema.
    """
    __tablename__ = 'assure'
    
    # Primary key - adjust based on your actual table
    id = db.Column(db.Integer, primary_key=True)
    
    # Personal identification
    numero_assure = db.Column(db.String(50), unique=True, nullable=False)
    cin = db.Column(db.String(20), unique=True)
    nom = db.Column(db.String(100), nullable=False)
    prenom = db.Column(db.String(100), nullable=False)
    nom_jeune_fille = db.Column(db.String(100))
    
    # Personal details
    date_naissance = db.Column(db.Date)
    lieu_naissance = db.Column(db.String(100))
    sexe = db.Column(db.String(1))  # M/F
    nationalite = db.Column(db.String(50))
    situation_familiale = db.Column(db.String(50))
    
    # Contact information
    adresse = db.Column(db.Text)
    ville = db.Column(db.String(100))
    code_postal = db.Column(db.String(20))
    telephone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    
    # Employment information
    employeur_id = db.Column(db.Integer, db.ForeignKey('employeur.id'))
    numero_employeur = db.Column(db.String(50))
    date_affiliation = db.Column(db.Date)
    date_radiation = db.Column(db.Date)
    statut = db.Column(db.String(50), default='actif')
    
    # Professional details
    profession = db.Column(db.String(100))
    qualification = db.Column(db.String(100))
    salaire_base = db.Column(db.Numeric(10, 2))
    
    # Audit fields
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    updated_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # Relationships
    employeur = db.relationship('Employeur', backref='assures')
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_assures')
    updater = db.relationship('User', foreign_keys=[updated_by], backref='updated_assures')
    
    def to_dict(self):
        """Convert assure to dictionary"""
        return {
            'id': self.id,
            'numero_assure': self.numero_assure,
            'cin': self.cin,
            'nom': self.nom,
            'prenom': self.prenom,
            'nom_jeune_fille': self.nom_jeune_fille,
            'date_naissance': self.date_naissance.isoformat() if self.date_naissance else None,
            'lieu_naissance': self.lieu_naissance,
            'sexe': self.sexe,
            'nationalite': self.nationalite,
            'situation_familiale': self.situation_familiale,
            'adresse': self.adresse,
            'ville': self.ville,
            'code_postal': self.code_postal,
            'telephone': self.telephone,
            'email': self.email,
            'numero_employeur': self.numero_employeur,
            'date_affiliation': self.date_affiliation.isoformat() if self.date_affiliation else None,
            'date_radiation': self.date_radiation.isoformat() if self.date_radiation else None,
            'statut': self.statut,
            'profession': self.profession,
            'qualification': self.qualification,
            'salaire_base': float(self.salaire_base) if self.salaire_base else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f'<Assure {self.numero_assure}: {self.nom} {self.prenom}>'
