from app import db
from datetime import datetime

class Assure(db.Model):
    """
    Model for the existing 'assure' table in Oracle database.
    Based on the actual Oracle schema provided.
    """
    __tablename__ = 'assure'

    # Primary key - composite key from Oracle
    ass_mat = db.Column(db.Numeric(8), primary_key=True)
    ass_cle = db.Column(db.Numeric(2), primary_key=True)

    # Administrative codes
    bur_cod = db.Column(db.Numeric(3))
    pay_cod = db.Column(db.Numeric(4))

    # Employeur reference
    emp_mat = db.Column(db.Numeric(8))
    emp_cle = db.Column(db.Numeric(2))

    # Assure specific fields
    ass_iu = db.Column(db.Numeric(10))  # Identifiant unique
    ass_dteff = db.Column(db.Date)  # Date d'effet
    ass_dtimmat = db.Column(db.Date)  # Date d'immatriculation
    ass_dtsim = db.Column(db.Date)  # Date simulation
    ass_codag = db.Column(db.Nume<PERSON>(6))  # Code agent
    ass_dtvalid = db.Column(db.Date)  # Date validation
    ass_dttcent = db.Column(db.Date)  # Date traitement central
    ass_saluni = db.Column(db.Numeric(1))  # Salaire unique
    ass_rib = db.Column(db.Numeric(20))  # RIB
    ass_cnrps = db.Column(db.Numeric(10))  # CNRPS
    ass_brcreat = db.Column(db.Numeric(3))  # Bureau création
    ass_dtassuj = db.Column(db.Date)  # Date d'assujettissement
    ass_flpr = db.Column(db.Numeric(1))  # Flag PR
    ass_derreg = db.Column(db.Numeric(3))  # Dernier régime
    ass_agent = db.Column(db.Numeric(6))  # Agent

    # Additional fields
    etat_chargement = db.Column(db.String(100))  # État chargement
    ben_rc = db.Column(db.Numeric(12))  # Bénéficiaire RC
    methrecepdossid = db.Column(db.Numeric())  # Méthode réception dossier ID
    
    # Audit fields (added for application use)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    updated_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    # Relationships
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_assures')
    updater = db.relationship('User', foreign_keys=[updated_by], backref='updated_assures')
    
    def to_dict(self):
        """Convert assure to dictionary"""
        return {
            'ass_mat': int(self.ass_mat) if self.ass_mat else None,
            'ass_cle': int(self.ass_cle) if self.ass_cle else None,
            'emp_mat': int(self.emp_mat) if self.emp_mat else None,
            'emp_cle': int(self.emp_cle) if self.emp_cle else None,
            'ass_iu': int(self.ass_iu) if self.ass_iu else None,
            'bur_cod': int(self.bur_cod) if self.bur_cod else None,
            'pay_cod': int(self.pay_cod) if self.pay_cod else None,
            'ass_dteff': self.ass_dteff.isoformat() if self.ass_dteff else None,
            'ass_dtimmat': self.ass_dtimmat.isoformat() if self.ass_dtimmat else None,
            'ass_dtassuj': self.ass_dtassuj.isoformat() if self.ass_dtassuj else None,
            'ass_codag': int(self.ass_codag) if self.ass_codag else None,
            'ass_dtvalid': self.ass_dtvalid.isoformat() if self.ass_dtvalid else None,
            'ass_rib': int(self.ass_rib) if self.ass_rib else None,
            'ass_cnrps': int(self.ass_cnrps) if self.ass_cnrps else None,
            'ass_agent': int(self.ass_agent) if self.ass_agent else None,
            'etat_chargement': self.etat_chargement,
            'ben_rc': int(self.ben_rc) if self.ben_rc else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def __repr__(self):
        return f'<Assure {self.ass_mat}-{self.ass_cle}: {self.ass_iu}>'
