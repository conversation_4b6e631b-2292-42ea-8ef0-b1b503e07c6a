# Database Configuration
ORACLE_USER=your_oracle_username
OR<PERSON>LE_PASSWORD=your_oracle_password
ORACLE_HOST=localhost
ORACLE_PORT=1521
ORACLE_SERVICE_NAME=your_service_name

# Flask Configuration
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# Application Configuration
APP_NAME=CNSS API Management System
APP_VERSION=1.0.0
DEBUG=True
