import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { beneficiaireAPI } from '../services/api';
import LoadingSpinner from '../components/LoadingSpinner';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  HeartIcon,
} from '@heroicons/react/24/outline';

const Beneficiaires = () => {
  const [beneficiaires, setBeneficiaires] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchBeneficiaires();
  }, [currentPage, searchTerm]);

  const fetchBeneficiaires = async () => {
    try {
      setLoading(true);
      const response = await beneficiaireAPI.getAll({
        page: currentPage,
        per_page: 20,
        search: searchTerm,
      });
      
      setBeneficiaires(response.data.beneficiaires);
      setTotalPages(response.data.pagination.pages);
    } catch (err) {
      setError(err.response?.data?.message || 'Erreur lors du chargement des bénéficiaires');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleDelete = async (beneficiaire) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer le bénéficiaire ${beneficiaire.ben_nom} ${beneficiaire.ben_prenom}?`)) {
      try {
        await beneficiaireAPI.delete(beneficiaire.ben_iducnss);
        fetchBeneficiaires();
      } catch (err) {
        alert(err.response?.data?.message || 'Erreur lors de la suppression');
      }
    }
  };

  if (loading && beneficiaires.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Bénéficiaires</h1>
          <p className="mt-1 text-sm text-secondary-600">
            Gérez les bénéficiaires du système CNSS
          </p>
        </div>
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="btn-primary flex items-center"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          Nouveau Bénéficiaire
        </motion.button>
      </div>

      {/* Search */}
      <div className="card">
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-secondary-400" />
            <input
              type="text"
              placeholder="Rechercher par ID CNSS, nom, prénom, email..."
              value={searchTerm}
              onChange={handleSearch}
              className="input-field pl-10"
            />
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-error-50 border border-error-200 rounded-lg p-4">
          <p className="text-error-600">{error}</p>
        </div>
      )}

      {/* Table */}
      <div className="card p-0">
        <div className="table-container">
          <table className="min-w-full divide-y divide-secondary-200">
            <thead className="bg-secondary-50">
              <tr>
                <th className="table-header">ID CNSS</th>
                <th className="table-header">Nom & Prénom</th>
                <th className="table-header">Assuré</th>
                <th className="table-header">Date Naissance</th>
                <th className="table-header">Sexe</th>
                <th className="table-header">Téléphone</th>
                <th className="table-header">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-secondary-200">
              <AnimatePresence>
                {beneficiaires.map((beneficiaire, index) => (
                  <motion.tr
                    key={beneficiaire.ben_iducnss}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ delay: index * 0.05 }}
                    className="hover:bg-secondary-50"
                  >
                    <td className="table-cell">
                      <div className="flex items-center">
                        <HeartIcon className="h-5 w-5 text-secondary-400 mr-2" />
                        <span className="font-medium">
                          {beneficiaire.ben_iducnss}
                        </span>
                      </div>
                    </td>
                    <td className="table-cell">
                      <div>
                        <div className="font-medium text-secondary-900">
                          {beneficiaire.ben_nom} {beneficiaire.ben_prenom}
                        </div>
                        {beneficiaire.ben_nom_ar && (
                          <div className="text-sm text-secondary-500">
                            {beneficiaire.ben_nom_ar} {beneficiaire.ben_prn_ar}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="table-cell">
                      <span className="text-sm text-secondary-600">
                        {beneficiaire.ass_mat ? `${beneficiaire.ass_mat}-${beneficiaire.ass_cle}` : '-'}
                      </span>
                    </td>
                    <td className="table-cell">
                      <span className="text-sm text-secondary-600">
                        {beneficiaire.ben_dtnais ? new Date(beneficiaire.ben_dtnais).toLocaleDateString('fr-FR') : '-'}
                      </span>
                    </td>
                    <td className="table-cell">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        beneficiaire.ben_sexe === 1 
                          ? 'bg-blue-100 text-blue-800' 
                          : beneficiaire.ben_sexe === 0
                          ? 'bg-pink-100 text-pink-800'
                          : 'bg-secondary-100 text-secondary-800'
                      }`}>
                        {beneficiaire.ben_sexe === 1 ? 'M' : beneficiaire.ben_sexe === 0 ? 'F' : '-'}
                      </span>
                    </td>
                    <td className="table-cell">
                      <span className="text-sm text-secondary-600">
                        {beneficiaire.ben_tel || '-'}
                      </span>
                    </td>
                    <td className="table-cell">
                      <div className="flex items-center space-x-2">
                        <button
                          className="p-1 text-secondary-400 hover:text-primary-600 transition-colors"
                          title="Voir"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        <button
                          className="p-1 text-secondary-400 hover:text-warning-600 transition-colors"
                          title="Modifier"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(beneficiaire)}
                          className="p-1 text-secondary-400 hover:text-error-600 transition-colors"
                          title="Supprimer"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </AnimatePresence>
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-secondary-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-secondary-600">
                Page {currentPage} sur {totalPages}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Précédent
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Suivant
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Loading overlay */}
      {loading && beneficiaires.length > 0 && (
        <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center z-50">
          <LoadingSpinner size="large" />
        </div>
      )}
    </div>
  );
};

export default Beneficiaires;
