#!/bin/bash

echo "Starting CNSS Application Development Environment"
echo "================================================"

echo ""
echo "1. Starting Backend (Flask)..."
cd backend
gnome-terminal --title="CNSS Backend" -- bash -c "python app.py; exec bash" &
cd ..

echo ""
echo "2. Starting Frontend (React)..."
gnome-terminal --title="CNSS Frontend" -- bash -c "npm start; exec bash" &

echo ""
echo "✅ Both services are starting..."
echo "📱 Frontend: http://localhost:3000"
echo "🔧 Backend API: http://localhost:5000/api"
echo ""
echo "Press Ctrl+C to exit..."

# Keep script running
while true; do
    sleep 1
done
