import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useAuth } from "../contexts/AuthContext";
import { adminAPI, apiManagementAPI } from "../services/api";
import LoadingSpinner from "../components/LoadingSpinner";
import {
  BuildingOfficeIcon,
  UserGroupIcon,
  HeartIcon,
  ChartBarIcon,
  ClockIcon,
  ExclamationTriangleIcon,
} from "@heroicons/react/24/outline";

const Dashboard = () => {
  const { user, isAdmin } = useAuth();
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);

        if (isAdmin()) {
          // Admin gets comprehensive dashboard stats
          const response = await adminAPI.getDashboardStats();
          setStats(response.data.stats);
        } else {
          // Agents get API management stats
          const apiStats = await apiManagementAPI.getStats();

          setStats({
            data: {
              apis: apiStats.data.stats,
            },
          });
        }
      } catch (err) {
        setError(
          err.response?.data?.message ||
            "Erreur lors du chargement des statistiques"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [isAdmin]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-error-50 border border-error-200 rounded-lg p-4">
        <div className="flex">
          <ExclamationTriangleIcon className="h-5 w-5 text-error-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-error-800">Erreur</h3>
            <p className="mt-1 text-sm text-error-700">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  const StatCard = ({ title, value, icon: Icon, color, change }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="card hover:shadow-md transition-shadow duration-200"
    >
      <div className="flex items-center">
        <div className={`flex-shrink-0 p-3 rounded-lg ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
        <div className="ml-4 flex-1">
          <p className="text-sm font-medium text-secondary-600">{title}</p>
          <p className="text-2xl font-semibold text-secondary-900">{value}</p>
          {change && (
            <p
              className={`text-sm ${
                change >= 0 ? "text-success-600" : "text-error-600"
              }`}
            >
              {change >= 0 ? "+" : ""}
              {change}% ce mois
            </p>
          )}
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-secondary-900">
          Tableau de bord
        </h1>
        <p className="mt-1 text-sm text-secondary-600">
          Bienvenue, {user?.username}. Voici un aperçu de votre système CNSS.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <StatCard
          title="Mes APIs"
          value={stats?.data?.apis?.my_apis?.total || 0}
          icon={BuildingOfficeIcon}
          color="bg-primary-500"
        />
        <StatCard
          title="APIs Publiques"
          value={stats?.data?.apis?.usage?.public_apis_available || 0}
          icon={UserGroupIcon}
          color="bg-success-500"
        />
        <StatCard
          title="Appels Effectués"
          value={stats?.data?.apis?.usage?.calls_made || 0}
          icon={HeartIcon}
          color="bg-warning-500"
        />
      </div>

      {/* Admin-only sections */}
      {isAdmin() && stats?.users && (
        <>
          {/* User Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              title="Utilisateurs Total"
              value={stats.users.total}
              icon={UserGroupIcon}
              color="bg-secondary-500"
            />
            <StatCard
              title="Utilisateurs Actifs"
              value={stats.users.active}
              icon={UserGroupIcon}
              color="bg-success-500"
            />
            <StatCard
              title="Administrateurs"
              value={stats.users.admins}
              icon={UserGroupIcon}
              color="bg-primary-500"
            />
            <StatCard
              title="Agents"
              value={stats.users.agents}
              icon={UserGroupIcon}
              color="bg-warning-500"
            />
          </div>

          {/* Activity Stats */}
          {stats?.activity && (
            <div className="card">
              <h3 className="text-lg font-medium text-secondary-900 mb-4">
                Activité Récente (7 derniers jours)
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <p className="text-sm font-medium text-secondary-600 mb-2">
                    Actions Totales
                  </p>
                  <p className="text-3xl font-bold text-primary-600">
                    {stats.activity.recent_actions}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-secondary-600 mb-2">
                    Actions par Type
                  </p>
                  <div className="space-y-2">
                    {stats.activity.by_action
                      ?.slice(0, 5)
                      .map((action, index) => (
                        <div key={index} className="flex justify-between">
                          <span className="text-sm text-secondary-600">
                            {action.action}
                          </span>
                          <span className="text-sm font-medium text-secondary-900">
                            {action.count}
                          </span>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {/* Quick Actions */}
      <div className="card">
        <h3 className="text-lg font-medium text-secondary-900 mb-4">
          Actions Rapides
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="p-4 border border-secondary-200 rounded-lg hover:bg-secondary-50 transition-colors duration-200"
            onClick={() => (window.location.href = "/my-apis")}
          >
            <BuildingOfficeIcon className="h-8 w-8 text-primary-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-secondary-900">Mes APIs</p>
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="p-4 border border-secondary-200 rounded-lg hover:bg-secondary-50 transition-colors duration-200"
            onClick={() => (window.location.href = "/public-apis")}
          >
            <UserGroupIcon className="h-8 w-8 text-success-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-secondary-900">
              APIs Publiques
            </p>
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="p-4 border border-secondary-200 rounded-lg hover:bg-secondary-50 transition-colors duration-200"
            onClick={() => (window.location.href = "/api-calls")}
          >
            <HeartIcon className="h-8 w-8 text-warning-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-secondary-900">
              Historique Appels
            </p>
          </motion.button>
        </div>
      </div>

      {/* System Status */}
      <div className="card">
        <h3 className="text-lg font-medium text-secondary-900 mb-4">
          État du Système
        </h3>
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <div className="h-3 w-3 bg-success-400 rounded-full"></div>
            <span className="ml-2 text-sm text-secondary-600">
              Base de données
            </span>
          </div>
          <div className="flex items-center">
            <div className="h-3 w-3 bg-success-400 rounded-full"></div>
            <span className="ml-2 text-sm text-secondary-600">API</span>
          </div>
          <div className="flex items-center">
            <ClockIcon className="h-4 w-4 text-secondary-400" />
            <span className="ml-1 text-sm text-secondary-600">
              Dernière mise à jour: {new Date().toLocaleTimeString("fr-FR")}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
