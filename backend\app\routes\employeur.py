from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models.employeur import Employeur
from app.models.user import User
from app.utils.decorators import agent_or_admin_required, audit_log, validate_json
from datetime import datetime
from sqlalchemy import or_

employeur_bp = Blueprint('employeur', __name__)

@employeur_bp.route('/', methods=['GET'])
@agent_or_admin_required
@audit_log('READ', 'employeur')
def get_employeurs():
    """Get all employeurs with pagination and search"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '', type=str)
    
    # Build query
    query = Employeur.query
    
    # Apply search filter
    if search:
        query = query.filter(
            or_(
                Employeur.emp_mat.ilike(f'%{search}%'),
                Employeur.emp_rais.ilike(f'%{search}%'),
                Employeur.emp_email.ilike(f'%{search}%')
            )
        )
    
    # Apply pagination
    employeurs = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'message': 'Employeurs retrieved successfully',
        'employeurs': [emp.to_dict() for emp in employeurs.items],
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': employeurs.total,
            'pages': employeurs.pages,
            'has_next': employeurs.has_next,
            'has_prev': employeurs.has_prev
        }
    }), 200

@employeur_bp.route('/<int:emp_mat>', methods=['GET'])
@agent_or_admin_required
@audit_log('READ', 'employeur')
def get_employeur(emp_mat):
    """Get specific employeur by matricule"""
    employeur = Employeur.query.filter_by(emp_mat=emp_mat).first_or_404()

    return jsonify({
        'message': 'Employeur retrieved successfully',
        'employeur': employeur.to_dict()
    }), 200

@employeur_bp.route('/', methods=['POST'])
@agent_or_admin_required
@validate_json('emp_mat', 'emp_cle', 'emp_rais')
@audit_log('CREATE', 'employeur')
def create_employeur():
    """Create new employeur"""
    data = request.get_json()
    current_user_id = get_jwt_identity()

    # Check if employeur already exists
    existing = Employeur.query.filter_by(emp_mat=data['emp_mat'], emp_cle=data['emp_cle']).first()
    if existing:
        return jsonify({'message': 'Employeur already exists'}), 400

    # Create new employeur
    employeur = Employeur(
        emp_mat=data['emp_mat'],
        emp_cle=data['emp_cle'],
        emp_rais=data['emp_rais'],
        emp_rais_ar=data.get('emp_rais_ar'),
        emp_tel=data.get('emp_tel'),
        emp_fax=data.get('emp_fax'),
        emp_email=data.get('emp_email'),
        emp_sigle=data.get('emp_sigle'),
        emp_activite=data.get('emp_activite'),
        emp_dtaff=datetime.strptime(data['emp_dtaff'], '%Y-%m-%d').date() if data.get('emp_dtaff') else None,
        emp_dteff=datetime.strptime(data['emp_dteff'], '%Y-%m-%d').date() if data.get('emp_dteff') else None,
        emp_adrcor=data.get('emp_adrcor'),
        emp_adrcor_ar=data.get('emp_adrcor_ar'),
        pat_num=data.get('pat_num'),
        atv_cod=data.get('atv_cod'),
        bur_cod=data.get('bur_cod'),
        pay_cod=data.get('pay_cod'),
        created_by=current_user_id,
        updated_by=current_user_id
    )
    
    db.session.add(employeur)
    db.session.commit()
    
    return jsonify({
        'message': 'Employeur created successfully',
        'employeur': employeur.to_dict()
    }), 201

@employeur_bp.route('/<int:emp_mat>', methods=['PUT'])
@agent_or_admin_required
@validate_json('emp_rais')
@audit_log('UPDATE', 'employeur')
def update_employeur(emp_mat):
    """Update existing employeur"""
    employeur = Employeur.query.filter_by(emp_mat=emp_mat).first_or_404()
    data = request.get_json()
    current_user_id = get_jwt_identity()

    # Update fields
    employeur.emp_rais = data.get('emp_rais', employeur.emp_rais)
    employeur.emp_rais_ar = data.get('emp_rais_ar', employeur.emp_rais_ar)
    employeur.emp_tel = data.get('emp_tel', employeur.emp_tel)
    employeur.emp_fax = data.get('emp_fax', employeur.emp_fax)
    employeur.emp_email = data.get('emp_email', employeur.emp_email)
    employeur.emp_sigle = data.get('emp_sigle', employeur.emp_sigle)
    employeur.emp_activite = data.get('emp_activite', employeur.emp_activite)
    employeur.emp_adrcor = data.get('emp_adrcor', employeur.emp_adrcor)
    employeur.emp_adrcor_ar = data.get('emp_adrcor_ar', employeur.emp_adrcor_ar)
    employeur.pat_num = data.get('pat_num', employeur.pat_num)
    employeur.atv_cod = data.get('atv_cod', employeur.atv_cod)
    employeur.bur_cod = data.get('bur_cod', employeur.bur_cod)
    employeur.pay_cod = data.get('pay_cod', employeur.pay_cod)

    # Update dates
    if data.get('emp_dtaff'):
        employeur.emp_dtaff = datetime.strptime(data['emp_dtaff'], '%Y-%m-%d').date()
    if data.get('emp_dteff'):
        employeur.emp_dteff = datetime.strptime(data['emp_dteff'], '%Y-%m-%d').date()

    employeur.updated_by = current_user_id
    employeur.updated_at = datetime.now()
    
    db.session.commit()
    
    return jsonify({
        'message': 'Employeur updated successfully',
        'employeur': employeur.to_dict()
    }), 200

@employeur_bp.route('/<int:emp_mat>', methods=['DELETE'])
@agent_or_admin_required
@audit_log('DELETE', 'employeur')
def delete_employeur(emp_mat):
    """Delete employeur"""
    employeur = Employeur.query.filter_by(emp_mat=emp_mat).first_or_404()

    # Check if employeur has associated assures (you may need to implement this check)
    # For now, we'll allow deletion

    db.session.delete(employeur)
    db.session.commit()

    return jsonify({'message': 'Employeur deleted successfully'}), 200

@employeur_bp.route('/stats', methods=['GET'])
@agent_or_admin_required
@audit_log('READ', 'employeur')
def get_employeur_stats():
    """Get employeur statistics"""
    total_employeurs = Employeur.query.count()

    # Get employeurs by activity
    activities = db.session.query(
        Employeur.emp_activite,
        db.func.count(Employeur.emp_mat).label('count')
    ).group_by(Employeur.emp_activite).all()

    # Get employeurs by bureau
    bureaux = db.session.query(
        Employeur.bur_cod,
        db.func.count(Employeur.emp_mat).label('count')
    ).group_by(Employeur.bur_cod).all()

    return jsonify({
        'message': 'Employeur statistics retrieved successfully',
        'stats': {
            'total': total_employeurs,
            'by_activity': [{'activity': a[0], 'count': a[1]} for a in activities if a[0]],
            'by_bureau': [{'bureau': b[0], 'count': b[1]} for b in bureaux if b[0]]
        }
    }), 200
