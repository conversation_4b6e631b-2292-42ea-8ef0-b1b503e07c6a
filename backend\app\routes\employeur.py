from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models.employeur import Employeur
from app.models.user import User
from app.utils.decorators import agent_or_admin_required, audit_log, validate_json
from datetime import datetime
from sqlalchemy import or_

employeur_bp = Blueprint('employeur', __name__)

@employeur_bp.route('/', methods=['GET'])
@agent_or_admin_required
@audit_log('READ', 'employeur')
def get_employeurs():
    """Get all employeurs with pagination and search"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '', type=str)
    
    # Build query
    query = Employeur.query
    
    # Apply search filter
    if search:
        query = query.filter(
            or_(
                Employeur.numero_employeur.ilike(f'%{search}%'),
                Employeur.raison_sociale.ilike(f'%{search}%'),
                Employeur.email.ilike(f'%{search}%')
            )
        )
    
    # Apply pagination
    employeurs = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'message': 'Employeurs retrieved successfully',
        'employeurs': [emp.to_dict() for emp in employeurs.items],
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': employeurs.total,
            'pages': employeurs.pages,
            'has_next': employeurs.has_next,
            'has_prev': employeurs.has_prev
        }
    }), 200

@employeur_bp.route('/<int:id>', methods=['GET'])
@agent_or_admin_required
@audit_log('READ', 'employeur')
def get_employeur(id):
    """Get specific employeur by ID"""
    employeur = Employeur.query.get_or_404(id)
    
    return jsonify({
        'message': 'Employeur retrieved successfully',
        'employeur': employeur.to_dict()
    }), 200

@employeur_bp.route('/', methods=['POST'])
@agent_or_admin_required
@validate_json('numero_employeur', 'raison_sociale')
@audit_log('CREATE', 'employeur')
def create_employeur():
    """Create new employeur"""
    data = request.get_json()
    current_user_id = get_jwt_identity()
    
    # Check if numero_employeur already exists
    existing = Employeur.query.filter_by(numero_employeur=data['numero_employeur']).first()
    if existing:
        return jsonify({'message': 'Numero employeur already exists'}), 400
    
    # Create new employeur
    employeur = Employeur(
        numero_employeur=data['numero_employeur'],
        raison_sociale=data['raison_sociale'],
        forme_juridique=data.get('forme_juridique'),
        secteur_activite=data.get('secteur_activite'),
        adresse=data.get('adresse'),
        ville=data.get('ville'),
        code_postal=data.get('code_postal'),
        telephone=data.get('telephone'),
        fax=data.get('fax'),
        email=data.get('email'),
        date_affiliation=datetime.strptime(data['date_affiliation'], '%Y-%m-%d').date() if data.get('date_affiliation') else None,
        statut=data.get('statut', 'actif'),
        capital_social=data.get('capital_social'),
        chiffre_affaires=data.get('chiffre_affaires'),
        created_by=current_user_id,
        updated_by=current_user_id
    )
    
    db.session.add(employeur)
    db.session.commit()
    
    return jsonify({
        'message': 'Employeur created successfully',
        'employeur': employeur.to_dict()
    }), 201

@employeur_bp.route('/<int:id>', methods=['PUT'])
@agent_or_admin_required
@validate_json('raison_sociale')
@audit_log('UPDATE', 'employeur')
def update_employeur(id):
    """Update existing employeur"""
    employeur = Employeur.query.get_or_404(id)
    data = request.get_json()
    current_user_id = get_jwt_identity()
    
    # Check if numero_employeur is being changed and if it already exists
    if 'numero_employeur' in data and data['numero_employeur'] != employeur.numero_employeur:
        existing = Employeur.query.filter_by(numero_employeur=data['numero_employeur']).first()
        if existing:
            return jsonify({'message': 'Numero employeur already exists'}), 400
        employeur.numero_employeur = data['numero_employeur']
    
    # Update fields
    employeur.raison_sociale = data.get('raison_sociale', employeur.raison_sociale)
    employeur.forme_juridique = data.get('forme_juridique', employeur.forme_juridique)
    employeur.secteur_activite = data.get('secteur_activite', employeur.secteur_activite)
    employeur.adresse = data.get('adresse', employeur.adresse)
    employeur.ville = data.get('ville', employeur.ville)
    employeur.code_postal = data.get('code_postal', employeur.code_postal)
    employeur.telephone = data.get('telephone', employeur.telephone)
    employeur.fax = data.get('fax', employeur.fax)
    employeur.email = data.get('email', employeur.email)
    employeur.statut = data.get('statut', employeur.statut)
    employeur.capital_social = data.get('capital_social', employeur.capital_social)
    employeur.chiffre_affaires = data.get('chiffre_affaires', employeur.chiffre_affaires)
    
    if data.get('date_affiliation'):
        employeur.date_affiliation = datetime.strptime(data['date_affiliation'], '%Y-%m-%d').date()
    
    employeur.updated_by = current_user_id
    employeur.updated_at = datetime.utcnow()
    
    db.session.commit()
    
    return jsonify({
        'message': 'Employeur updated successfully',
        'employeur': employeur.to_dict()
    }), 200

@employeur_bp.route('/<int:id>', methods=['DELETE'])
@agent_or_admin_required
@audit_log('DELETE', 'employeur')
def delete_employeur(id):
    """Delete employeur"""
    employeur = Employeur.query.get_or_404(id)
    
    # Check if employeur has associated assures
    if employeur.assures:
        return jsonify({
            'message': 'Cannot delete employeur with associated assures'
        }), 400
    
    db.session.delete(employeur)
    db.session.commit()
    
    return jsonify({'message': 'Employeur deleted successfully'}), 200

@employeur_bp.route('/stats', methods=['GET'])
@agent_or_admin_required
@audit_log('READ', 'employeur')
def get_employeur_stats():
    """Get employeur statistics"""
    total_employeurs = Employeur.query.count()
    active_employeurs = Employeur.query.filter_by(statut='actif').count()
    inactive_employeurs = total_employeurs - active_employeurs
    
    # Get employeurs by sector
    sectors = db.session.query(
        Employeur.secteur_activite,
        db.func.count(Employeur.id).label('count')
    ).group_by(Employeur.secteur_activite).all()
    
    return jsonify({
        'message': 'Employeur statistics retrieved successfully',
        'stats': {
            'total': total_employeurs,
            'active': active_employeurs,
            'inactive': inactive_employeurs,
            'by_sector': [{'sector': s[0], 'count': s[1]} for s in sectors if s[0]]
        }
    }), 200
