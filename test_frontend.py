#!/usr/bin/env python3
"""
Test script to verify the frontend and backend integration
"""

import requests
import time

def test_frontend():
    """Test if frontend is accessible"""
    print("🌐 Testing Frontend...")
    try:
        response = requests.get("http://localhost:3000", timeout=10)
        if response.status_code == 200 and "<!DOCTYPE html>" in response.text:
            print("✅ Frontend is running successfully on http://localhost:3000")
            return True
        else:
            print(f"❌ Frontend returned status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Frontend is not accessible on http://localhost:3000")
        return False
    except Exception as e:
        print(f"❌ Frontend test failed: {e}")
        return False

def test_backend():
    """Test if backend is accessible"""
    print("\n🔧 Testing Backend...")
    try:
        response = requests.get("http://localhost:5000/api/auth/login", timeout=10)
        # We expect a 405 (Method Not Allowed) for GET on login endpoint
        if response.status_code in [405, 400]:
            print("✅ Backend is running successfully on http://localhost:5000")
            return True
        else:
            print(f"❌ Backend returned unexpected status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Backend is not accessible on http://localhost:5000")
        return False
    except Exception as e:
        print(f"❌ Backend test failed: {e}")
        return False

def test_login_api():
    """Test login functionality"""
    print("\n🔐 Testing Login API...")
    try:
        response = requests.post("http://localhost:5000/api/auth/login", 
                               json={"username": "admin", "password": "admin123"},
                               timeout=10)
        if response.status_code == 200:
            data = response.json()
            if "access_token" in data:
                print("✅ Login API working correctly")
                return True
            else:
                print("❌ Login API response missing access_token")
                return False
        else:
            print(f"❌ Login failed with status {response.status_code}: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Login test failed: {e}")
        return False

def test_cors():
    """Test CORS configuration"""
    print("\n🌍 Testing CORS Configuration...")
    try:
        # Test preflight request
        response = requests.options("http://localhost:5000/api/auth/login",
                                  headers={
                                      "Origin": "http://localhost:3000",
                                      "Access-Control-Request-Method": "POST",
                                      "Access-Control-Request-Headers": "Content-Type"
                                  },
                                  timeout=10)
        
        if response.status_code in [200, 204]:
            cors_headers = response.headers.get("Access-Control-Allow-Origin", "")
            if "*" in cors_headers or "localhost:3000" in cors_headers:
                print("✅ CORS is configured correctly")
                return True
            else:
                print(f"❌ CORS headers not found: {cors_headers}")
                return False
        else:
            print(f"❌ CORS preflight failed with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ CORS test failed: {e}")
        return False

def main():
    """Run all integration tests"""
    print("🚀 CNSS Frontend-Backend Integration Test")
    print("=" * 50)
    
    tests = [
        ("Frontend Accessibility", test_frontend),
        ("Backend Accessibility", test_backend),
        ("Login API", test_login_api),
        ("CORS Configuration", test_cors),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            time.sleep(1)  # Small delay between tests
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Integration Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All integration tests passed!")
        print("\n📝 Your CNSS Application is fully functional:")
        print("   • Frontend: http://localhost:3000")
        print("   • Backend:  http://localhost:5000")
        print("   • Login:    admin/admin123 or agent1/agent123")
        print("\n🚀 Ready to use!")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ Tests interrupted by user")
        exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        exit(1)
