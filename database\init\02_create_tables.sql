-- Create tables for CNSS application
-- Connect as cnss_user
CONNECT cnss_user/cnss_password@XEPDB1;

-- Create Users table
CREATE TABLE users (
    id NUMBER PRIMARY KEY,
    username VARCHAR2(80) UNIQUE NOT NULL,
    email VARCHAR2(120) UNIQUE NOT NULL,
    password_hash VARCHAR2(255) NOT NULL,
    role VARCHAR2(20) CHECK (role IN ('agent', 'admin')) DEFAULT 'agent',
    organization VARCHAR2(100),
    is_active NUMBER(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP
);

-- Create trigger for users ID
CREATE OR REPLACE TRIGGER users_id_trigger
    BEFORE INSERT ON users
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := users_seq.NEXTVAL;
    END IF;
END;

-- Create Employeur table
CREATE TABLE employeur (
    id NUMBER PRIMARY KEY,
    numero_employeur VARCHAR2(50) UNIQUE NOT NULL,
    raison_sociale VARCHAR2(200) NOT NULL,
    forme_juridique VARCHAR2(100),
    secteur_activite VARCHAR2(100),
    adresse CLOB,
    ville VARCHAR2(100),
    code_postal VARCHAR2(20),
    telephone VARCHAR2(20),
    fax VARCHAR2(20),
    email VARCHAR2(120),
    date_affiliation DATE,
    statut VARCHAR2(50) DEFAULT 'actif',
    capital_social NUMBER(15,2),
    chiffre_affaires NUMBER(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER REFERENCES users(id),
    updated_by NUMBER REFERENCES users(id)
);

-- Create trigger for employeur ID
CREATE OR REPLACE TRIGGER employeur_id_trigger
    BEFORE INSERT ON employeur
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := employeur_seq.NEXTVAL;
    END IF;
END;

-- Create Assure table
CREATE TABLE assure (
    id NUMBER PRIMARY KEY,
    numero_assure VARCHAR2(50) UNIQUE NOT NULL,
    cin VARCHAR2(20) UNIQUE,
    nom VARCHAR2(100) NOT NULL,
    prenom VARCHAR2(100) NOT NULL,
    nom_jeune_fille VARCHAR2(100),
    date_naissance DATE,
    lieu_naissance VARCHAR2(100),
    sexe VARCHAR2(1) CHECK (sexe IN ('M', 'F')),
    nationalite VARCHAR2(50),
    situation_familiale VARCHAR2(50),
    adresse CLOB,
    ville VARCHAR2(100),
    code_postal VARCHAR2(20),
    telephone VARCHAR2(20),
    email VARCHAR2(120),
    employeur_id NUMBER REFERENCES employeur(id),
    numero_employeur VARCHAR2(50),
    date_affiliation DATE,
    date_radiation DATE,
    statut VARCHAR2(50) DEFAULT 'actif',
    profession VARCHAR2(100),
    qualification VARCHAR2(100),
    salaire_base NUMBER(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER REFERENCES users(id),
    updated_by NUMBER REFERENCES users(id)
);

-- Create trigger for assure ID
CREATE OR REPLACE TRIGGER assure_id_trigger
    BEFORE INSERT ON assure
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := assure_seq.NEXTVAL;
    END IF;
END;

-- Create Beneficiaire table
CREATE TABLE beneficiaire (
    id NUMBER PRIMARY KEY,
    numero_beneficiaire VARCHAR2(50) UNIQUE NOT NULL,
    cin VARCHAR2(20),
    nom VARCHAR2(100) NOT NULL,
    prenom VARCHAR2(100) NOT NULL,
    nom_jeune_fille VARCHAR2(100),
    date_naissance DATE,
    lieu_naissance VARCHAR2(100),
    sexe VARCHAR2(1) CHECK (sexe IN ('M', 'F')),
    nationalite VARCHAR2(50),
    adresse CLOB,
    ville VARCHAR2(100),
    code_postal VARCHAR2(20),
    telephone VARCHAR2(20),
    email VARCHAR2(120),
    assure_id NUMBER REFERENCES assure(id),
    numero_assure VARCHAR2(50),
    lien_parente VARCHAR2(50),
    type_beneficiaire VARCHAR2(50),
    date_ouverture_droits DATE,
    date_fermeture_droits DATE,
    statut VARCHAR2(50) DEFAULT 'actif',
    numero_carte_soins VARCHAR2(50),
    date_emission_carte DATE,
    date_expiration_carte DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by NUMBER REFERENCES users(id),
    updated_by NUMBER REFERENCES users(id)
);

-- Create trigger for beneficiaire ID
CREATE OR REPLACE TRIGGER beneficiaire_id_trigger
    BEFORE INSERT ON beneficiaire
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := beneficiaire_seq.NEXTVAL;
    END IF;
END;

-- Create Audit Logs table
CREATE TABLE audit_logs (
    id NUMBER PRIMARY KEY,
    user_id NUMBER REFERENCES users(id),
    username VARCHAR2(80),
    user_role VARCHAR2(20),
    action VARCHAR2(50) NOT NULL,
    resource_type VARCHAR2(50),
    resource_id VARCHAR2(50),
    endpoint VARCHAR2(200),
    method VARCHAR2(10),
    ip_address VARCHAR2(45),
    user_agent CLOB,
    request_data CLOB,
    status_code NUMBER,
    response_message CLOB,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    duration_ms NUMBER,
    organization VARCHAR2(100),
    notes CLOB
);

-- Create trigger for audit_logs ID
CREATE OR REPLACE TRIGGER audit_logs_id_trigger
    BEFORE INSERT ON audit_logs
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        :NEW.id := audit_logs_seq.NEXTVAL;
    END IF;
END;

-- Create indexes for better performance
CREATE INDEX idx_employeur_numero ON employeur(numero_employeur);
CREATE INDEX idx_employeur_statut ON employeur(statut);
CREATE INDEX idx_assure_numero ON assure(numero_assure);
CREATE INDEX idx_assure_cin ON assure(cin);
CREATE INDEX idx_assure_employeur ON assure(employeur_id);
CREATE INDEX idx_assure_statut ON assure(statut);
CREATE INDEX idx_beneficiaire_numero ON beneficiaire(numero_beneficiaire);
CREATE INDEX idx_beneficiaire_assure ON beneficiaire(assure_id);
CREATE INDEX idx_beneficiaire_statut ON beneficiaire(statut);
CREATE INDEX idx_audit_logs_user ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);

COMMIT;
