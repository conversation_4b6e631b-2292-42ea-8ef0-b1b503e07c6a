#!/usr/bin/env python3
"""
Test script to verify the CNSS application setup
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all required modules can be imported"""
    try:
        print("Testing imports...")
        
        # Test Flask imports
        from flask import Flask
        print("✓ Flask imported successfully")
        
        # Test SQLAlchemy
        from flask_sqlalchemy import SQLAlchemy
        print("✓ SQLAlchemy imported successfully")
        
        # Test JWT
        from flask_jwt_extended import JWTManager
        print("✓ JWT imported successfully")
        
        # Test Oracle driver
        import cx_Oracle
        print("✓ cx_Oracle imported successfully")
        
        # Test app creation
        from app import create_app
        print("✓ App factory imported successfully")
        
        # Test models
        from app.models import User, Employeur, Assure, Beneficiaire, AuditLog
        print("✓ All models imported successfully")
        
        print("\n✅ All imports successful!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_app_creation():
    """Test if the Flask app can be created"""
    try:
        print("\nTesting app creation...")
        from app import create_app
        
        app = create_app()
        print("✓ Flask app created successfully")
        
        # Test app context
        with app.app_context():
            print("✓ App context works")
            
            # Test database connection (without actually connecting)
            from app import db
            print("✓ Database object accessible")
        
        print("✅ App creation successful!")
        return True
        
    except Exception as e:
        print(f"❌ App creation error: {e}")
        return False

def test_routes():
    """Test if routes are registered"""
    try:
        print("\nTesting route registration...")
        from app import create_app
        
        app = create_app()
        
        # Get all registered routes
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append(f"{rule.methods} {rule.rule}")
        
        # Check for key routes
        expected_routes = [
            '/api/auth/login',
            '/api/employeur',
            '/api/assure',
            '/api/beneficiaire',
            '/api/admin/users',
            '/api/logs'
        ]
        
        for expected in expected_routes:
            found = any(expected in route for route in routes)
            if found:
                print(f"✓ Route {expected} registered")
            else:
                print(f"❌ Route {expected} not found")
        
        print(f"\n📊 Total routes registered: {len(routes)}")
        print("✅ Route registration test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Route registration error: {e}")
        return False

def test_config():
    """Test configuration"""
    try:
        print("\nTesting configuration...")
        from config.config import Config
        
        config = Config()
        print("✓ Configuration loaded")
        
        # Check required config values
        required_configs = [
            'SECRET_KEY',
            'SQLALCHEMY_DATABASE_URI',
            'JWT_SECRET_KEY'
        ]
        
        for config_name in required_configs:
            if hasattr(config, config_name):
                value = getattr(config, config_name)
                if value:
                    print(f"✓ {config_name} is set")
                else:
                    print(f"⚠️  {config_name} is empty")
            else:
                print(f"❌ {config_name} not found")
        
        print("✅ Configuration test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 CNSS Application Setup Test")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_config,
        test_app_creation,
        test_routes
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 40)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your setup looks good.")
        print("\n📝 Next steps:")
        print("1. Set up your Oracle database connection")
        print("2. Run: python app.py")
        print("3. Test the API endpoints")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        print("\n🔧 Common fixes:")
        print("1. Install missing dependencies: pip install -r requirements.txt")
        print("2. Check your Oracle client installation")
        print("3. Verify your .env configuration")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
