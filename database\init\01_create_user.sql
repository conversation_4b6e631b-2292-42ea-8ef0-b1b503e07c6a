-- Create CNSS user and grant permissions
-- This script runs when the Oracle container starts

-- Connect as SYSTEM user
CONNECT system/OraclePassword123@XEPDB1;

-- Create the CNSS user
CREATE USER cnss_user IDENTIFIED BY cnss_password;

-- Grant necessary privileges
GRANT CONNECT, RESOURCE TO cnss_user;
GRANT CREATE SESSION TO cnss_user;
GRANT CREATE TABLE TO cnss_user;
GRANT CREATE SEQUENCE TO cnss_user;
GRANT CREATE VIEW TO cnss_user;
GRANT CREATE PROCEDURE TO cnss_user;
GRANT CREATE TRIGGER TO cnss_user;
GRANT UNLIMITED TABLESPACE TO cnss_user;

-- Grant additional privileges for application
GRANT SELECT ANY TABLE TO cnss_user;
GRANT INSERT ANY TABLE TO cnss_user;
GRANT UPDATE ANY TABLE TO cnss_user;
GRANT DELETE ANY TABLE TO cnss_user;

-- Connect as the new user
CONNECT cnss_user/cnss_password@XEPDB1;

-- Create sequences for primary keys
CREATE SEQUENCE employeur_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE assure_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE beneficiaire_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE users_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE audit_logs_seq START WITH 1 INCREMENT BY 1;

COMMIT;
