from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models.user import User
from app.models.employeur import Employeur
from app.models.assure import Assure
from app.models.beneficiaire import Beneficiaire
from app.models.audit_log import AuditLog
from app.utils.decorators import admin_required, audit_log, validate_json
from datetime import datetime
from sqlalchemy import or_, func

admin_bp = Blueprint('admin', __name__)

@admin_bp.route('/users', methods=['GET'])
@admin_required
@audit_log('READ', 'user')
def get_users():
    """Get all users with pagination and search"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '', type=str)
    role = request.args.get('role', '', type=str)
    
    # Build query
    query = User.query
    
    # Filter by role if specified
    if role:
        query = query.filter_by(role=role)
    
    # Apply search filter
    if search:
        query = query.filter(
            or_(
                User.username.ilike(f'%{search}%'),
                User.email.ilike(f'%{search}%'),
                User.organization.ilike(f'%{search}%')
            )
        )
    
    # Apply pagination
    users = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'message': 'Users retrieved successfully',
        'users': [user.to_dict() for user in users.items],
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': users.total,
            'pages': users.pages,
            'has_next': users.has_next,
            'has_prev': users.has_prev
        }
    }), 200

@admin_bp.route('/users', methods=['POST'])
@admin_required
@validate_json('username', 'email', 'password', 'role')
@audit_log('CREATE', 'user')
def create_user():
    """Create new user"""
    data = request.get_json()
    
    # Check if username already exists
    if User.query.filter_by(username=data['username']).first():
        return jsonify({'message': 'Username already exists'}), 400
    
    # Check if email already exists
    if User.query.filter_by(email=data['email']).first():
        return jsonify({'message': 'Email already exists'}), 400
    
    # Validate role
    if data['role'] not in ['agent', 'admin']:
        return jsonify({'message': 'Invalid role. Must be agent or admin'}), 400
    
    # Create new user
    user = User(
        username=data['username'],
        email=data['email'],
        role=data['role'],
        organization=data.get('organization', ''),
        is_active=data.get('is_active', True)
    )
    user.set_password(data['password'])
    
    db.session.add(user)
    db.session.commit()
    
    return jsonify({
        'message': 'User created successfully',
        'user': user.to_dict()
    }), 201

@admin_bp.route('/users/<int:id>', methods=['PUT'])
@admin_required
@validate_json('email', 'role')
@audit_log('UPDATE', 'user')
def update_user(id):
    """Update existing user"""
    user = User.query.get_or_404(id)
    data = request.get_json()
    
    # Check if email is being changed and if it already exists
    if 'email' in data and data['email'] != user.email:
        existing = User.query.filter_by(email=data['email']).first()
        if existing:
            return jsonify({'message': 'Email already exists'}), 400
        user.email = data['email']
    
    # Validate role
    if 'role' in data and data['role'] not in ['agent', 'admin']:
        return jsonify({'message': 'Invalid role. Must be agent or admin'}), 400
    
    # Update fields
    user.role = data.get('role', user.role)
    user.organization = data.get('organization', user.organization)
    user.is_active = data.get('is_active', user.is_active)
    user.updated_at = datetime.utcnow()
    
    db.session.commit()
    
    return jsonify({
        'message': 'User updated successfully',
        'user': user.to_dict()
    }), 200

@admin_bp.route('/users/<int:id>', methods=['DELETE'])
@admin_required
@audit_log('DELETE', 'user')
def delete_user(id):
    """Delete user"""
    user = User.query.get_or_404(id)
    current_user_id = get_jwt_identity()
    
    # Prevent admin from deleting themselves
    if user.id == current_user_id:
        return jsonify({'message': 'Cannot delete your own account'}), 400
    
    db.session.delete(user)
    db.session.commit()
    
    return jsonify({'message': 'User deleted successfully'}), 200

@admin_bp.route('/users/<int:id>/reset-password', methods=['POST'])
@admin_required
@validate_json('new_password')
@audit_log('RESET_PASSWORD', 'user')
def reset_user_password(id):
    """Reset user password"""
    user = User.query.get_or_404(id)
    data = request.get_json()
    
    new_password = data.get('new_password')
    
    # Validate new password
    if len(new_password) < 6:
        return jsonify({'message': 'Password must be at least 6 characters long'}), 400
    
    # Update password
    user.set_password(new_password)
    user.updated_at = datetime.utcnow()
    db.session.commit()
    
    return jsonify({'message': 'Password reset successfully'}), 200

@admin_bp.route('/dashboard/stats', methods=['GET'])
@admin_required
@audit_log('READ', 'dashboard')
def get_dashboard_stats():
    """Get dashboard statistics"""
    # User statistics
    total_users = User.query.count()
    active_users = User.query.filter_by(is_active=True).count()
    admin_users = User.query.filter_by(role='admin').count()
    agent_users = User.query.filter_by(role='agent').count()
    
    # Data statistics
    total_employeurs = Employeur.query.count()
    active_employeurs = Employeur.query.filter_by(statut='actif').count()
    total_assures = Assure.query.count()
    active_assures = Assure.query.filter_by(statut='actif').count()
    total_beneficiaires = Beneficiaire.query.count()
    active_beneficiaires = Beneficiaire.query.filter_by(statut='actif').count()
    
    # Recent activity (last 7 days)
    from datetime import timedelta
    week_ago = datetime.utcnow() - timedelta(days=7)
    recent_logs = AuditLog.query.filter(AuditLog.timestamp >= week_ago).count()
    
    # Activity by action type
    activity_stats = db.session.query(
        AuditLog.action,
        func.count(AuditLog.id).label('count')
    ).filter(AuditLog.timestamp >= week_ago).group_by(AuditLog.action).all()
    
    return jsonify({
        'message': 'Dashboard statistics retrieved successfully',
        'stats': {
            'users': {
                'total': total_users,
                'active': active_users,
                'admins': admin_users,
                'agents': agent_users
            },
            'data': {
                'employeurs': {
                    'total': total_employeurs,
                    'active': active_employeurs
                },
                'assures': {
                    'total': total_assures,
                    'active': active_assures
                },
                'beneficiaires': {
                    'total': total_beneficiaires,
                    'active': active_beneficiaires
                }
            },
            'activity': {
                'recent_actions': recent_logs,
                'by_action': [{'action': a[0], 'count': a[1]} for a in activity_stats]
            }
        }
    }), 200

@admin_bp.route('/system/health', methods=['GET'])
@admin_required
def system_health():
    """Get system health status"""
    try:
        # Test database connection
        db.session.execute('SELECT 1')
        db_status = 'healthy'
    except Exception as e:
        db_status = f'error: {str(e)}'
    
    return jsonify({
        'message': 'System health check completed',
        'status': {
            'database': db_status,
            'timestamp': datetime.utcnow().isoformat()
        }
    }), 200
