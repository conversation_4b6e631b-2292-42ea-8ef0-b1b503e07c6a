import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { apiManagementAPI } from "../services/api";
import LoadingSpinner from "../components/LoadingSpinner";
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  CodeBracketIcon,
  GlobeAltIcon,
  LockClosedIcon,
} from "@heroicons/react/24/outline";

const MyAPIs = () => {
  const [apis, setApis] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const fetchAPIs = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiManagementAPI.getMyAPIs({
        page: currentPage,
        per_page: 20,
        search: searchTerm,
      });

      setApis(response.data.apis);
      setTotalPages(response.data.pagination.pages);
    } catch (err) {
      setError(
        err.response?.data?.message || "Erreur lors du chargement des APIs"
      );
    } finally {
      setLoading(false);
    }
  }, [currentPage, searchTerm]);

  useEffect(() => {
    fetchAPIs();
  }, [fetchAPIs]);

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleView = (api) => {
    alert(
      `Voir API: ${api.name}\nURL: ${api.endpoint_url}\nDescription: ${api.description}`
    );
  };

  const handleEdit = (api) => {
    alert(`Modifier API: ${api.name}\n(Fonctionnalité à implémenter)`);
  };

  const handleCreate = () => {
    alert("Créer une nouvelle API\n(Fonctionnalité à implémenter)");
  };

  const handleDelete = async (api) => {
    if (
      window.confirm(`Êtes-vous sûr de vouloir supprimer l'API ${api.name}?`)
    ) {
      try {
        await apiManagementAPI.deleteAPI(api.id);
        fetchAPIs();
      } catch (err) {
        alert(err.response?.data?.message || "Erreur lors de la suppression");
      }
    }
  };

  const getMethodColor = (method) => {
    switch (method) {
      case "GET":
        return "bg-blue-100 text-blue-800";
      case "POST":
        return "bg-green-100 text-green-800";
      case "PUT":
        return "bg-yellow-100 text-yellow-800";
      case "DELETE":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (loading && apis.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Mes APIs</h1>
          <p className="mt-1 text-sm text-secondary-600">
            Gérez les APIs de votre organisation
          </p>
        </div>
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={handleCreate}
          className="btn-primary flex items-center"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          Nouvelle API
        </motion.button>
      </div>

      {/* Search */}
      <div className="card">
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-secondary-400" />
            <input
              type="text"
              placeholder="Rechercher par nom, description, URL..."
              value={searchTerm}
              onChange={handleSearch}
              className="input-field pl-10"
            />
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-error-50 border border-error-200 rounded-lg p-4">
          <p className="text-error-600">{error}</p>
        </div>
      )}

      {/* APIs Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <AnimatePresence>
          {apis.map((api, index) => (
            <motion.div
              key={api.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ delay: index * 0.05 }}
              className="card hover:shadow-md transition-shadow duration-200"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  <CodeBracketIcon className="h-8 w-8 text-primary-600 mr-3" />
                  <div>
                    <h3 className="font-semibold text-secondary-900">
                      {api.name}
                    </h3>
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getMethodColor(
                        api.method
                      )}`}
                    >
                      {api.method}
                    </span>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  {api.is_public ? (
                    <GlobeAltIcon
                      className="h-4 w-4 text-success-600"
                      title="Public"
                    />
                  ) : (
                    <LockClosedIcon
                      className="h-4 w-4 text-warning-600"
                      title="Privé"
                    />
                  )}
                </div>
              </div>

              <p className="text-sm text-secondary-600 mb-4 line-clamp-2">
                {api.description || "Aucune description"}
              </p>

              <div className="mb-4">
                <p className="text-xs text-secondary-500 mb-1">Endpoint:</p>
                <p className="text-sm font-mono bg-secondary-50 p-2 rounded text-secondary-800 break-all">
                  {api.endpoint_url}
                </p>
              </div>

              <div className="flex items-center justify-between text-sm text-secondary-500 mb-4">
                <span>Appels: {api.total_calls}</span>
                <span
                  className={`px-2 py-1 rounded-full text-xs ${
                    api.is_active
                      ? "bg-success-100 text-success-800"
                      : "bg-error-100 text-error-800"
                  }`}
                >
                  {api.is_active ? "Actif" : "Inactif"}
                </span>
              </div>

              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleView(api)}
                  className="p-1 text-secondary-400 hover:text-primary-600 transition-colors"
                  title="Voir"
                >
                  <EyeIcon className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleEdit(api)}
                  className="p-1 text-secondary-400 hover:text-warning-600 transition-colors"
                  title="Modifier"
                >
                  <PencilIcon className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDelete(api)}
                  className="p-1 text-secondary-400 hover:text-error-600 transition-colors"
                  title="Supprimer"
                >
                  <TrashIcon className="h-4 w-4" />
                </button>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Empty State */}
      {!loading && apis.length === 0 && (
        <div className="text-center py-12">
          <CodeBracketIcon className="mx-auto h-12 w-12 text-secondary-400" />
          <h3 className="mt-2 text-sm font-medium text-secondary-900">
            Aucune API
          </h3>
          <p className="mt-1 text-sm text-secondary-500">
            Commencez par créer votre première API.
          </p>
          <div className="mt-6">
            <button onClick={handleCreate} className="btn-primary">
              <PlusIcon className="h-5 w-5 mr-2" />
              Créer une API
            </button>
          </div>
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-secondary-600">
            Page {currentPage} sur {totalPages}
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Précédent
            </button>
            <button
              onClick={() =>
                setCurrentPage(Math.min(totalPages, currentPage + 1))
              }
              disabled={currentPage === totalPages}
              className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Suivant
            </button>
          </div>
        </div>
      )}

      {/* Loading overlay */}
      {loading && apis.length > 0 && (
        <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center z-50">
          <LoadingSpinner size="large" />
        </div>
      )}
    </div>
  );
};

export default MyAPIs;
