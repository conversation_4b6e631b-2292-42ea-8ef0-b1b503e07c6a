@echo off
echo Starting CNSS Application Development Environment
echo ================================================

echo.
echo 1. Starting Backend (Flask)...
cd backend
start "CNSS Backend" cmd /k "python app.py"
cd ..

echo.
echo 2. Starting Frontend (React)...
start "CNSS Frontend" cmd /k "npm start"

echo.
echo ✅ Both services are starting...
echo 📱 Frontend: http://localhost:3000
echo 🔧 Backend API: http://localhost:5000/api
echo.
echo Press any key to exit...
pause > nul
