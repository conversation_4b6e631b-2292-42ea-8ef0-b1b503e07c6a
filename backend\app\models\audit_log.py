from app import db
from datetime import datetime
import json

class AuditLog(db.Model):
    """
    Model for tracking user actions and API usage for audit purposes
    """
    __tablename__ = 'audit_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # User information
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=True)
    username = db.Column(db.String(80))
    user_role = db.Column(db.String(20))
    
    # Action details
    action = db.Column(db.String(50), nullable=False)  # CREATE, READ, UPDATE, DELETE, LOGIN, LOGOUT
    resource_type = db.Column(db.String(50))  # employeur, assure, beneficiaire, user
    resource_id = db.Column(db.String(50))
    endpoint = db.Column(db.String(200))
    method = db.Column(db.String(10))  # GET, POST, PUT, DELETE
    
    # Request details
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)
    request_data = db.Column(db.Text)  # JSON string of request data
    
    # Response details
    status_code = db.Column(db.Integer)
    response_message = db.Column(db.Text)
    
    # Timing
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    duration_ms = db.Column(db.Integer)  # Request duration in milliseconds
    
    # Additional context
    organization = db.Column(db.String(100))
    notes = db.Column(db.Text)
    
    # Relationships
    user = db.relationship('User', backref='audit_logs')
    
    def set_request_data(self, data):
        """Set request data as JSON string"""
        if data:
            self.request_data = json.dumps(data, default=str)
    
    def get_request_data(self):
        """Get request data as Python object"""
        if self.request_data:
            try:
                return json.loads(self.request_data)
            except json.JSONDecodeError:
                return None
        return None
    
    def to_dict(self):
        """Convert audit log to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'username': self.username,
            'user_role': self.user_role,
            'action': self.action,
            'resource_type': self.resource_type,
            'resource_id': self.resource_id,
            'endpoint': self.endpoint,
            'method': self.method,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'request_data': self.get_request_data(),
            'status_code': self.status_code,
            'response_message': self.response_message,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'duration_ms': self.duration_ms,
            'organization': self.organization,
            'notes': self.notes
        }
    
    @staticmethod
    def log_action(user_id, action, resource_type=None, resource_id=None, 
                   endpoint=None, method=None, ip_address=None, user_agent=None,
                   request_data=None, status_code=None, response_message=None,
                   duration_ms=None, organization=None, notes=None):
        """Create a new audit log entry"""
        from app.models.user import User
        
        user = User.query.get(user_id) if user_id else None
        
        log_entry = AuditLog(
            user_id=user_id,
            username=user.username if user else 'Anonymous',
            user_role=user.role if user else 'Unknown',
            action=action,
            resource_type=resource_type,
            resource_id=str(resource_id) if resource_id else None,
            endpoint=endpoint,
            method=method,
            ip_address=ip_address,
            user_agent=user_agent,
            status_code=status_code,
            response_message=response_message,
            duration_ms=duration_ms,
            organization=organization or (user.organization if user else None),
            notes=notes
        )
        
        if request_data:
            log_entry.set_request_data(request_data)
        
        db.session.add(log_entry)
        db.session.commit()
        
        return log_entry
    
    def __repr__(self):
        return f'<AuditLog {self.id}: {self.username} - {self.action} - {self.timestamp}>'
