#!/usr/bin/env python3
"""
Test script to verify the CNSS API is working
"""

import requests
import json

BASE_URL = "http://localhost:5000/api"

def test_login():
    """Test login functionality"""
    print("Testing login...")
    
    # Test admin login
    response = requests.post(f"{BASE_URL}/auth/login", json={
        "username": "admin",
        "password": "admin123"
    })
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Admin login successful!")
        return data.get('access_token')
    else:
        print(f"❌ Admin login failed: {response.status_code} - {response.text}")
        return None

def test_my_apis(token):
    """Test getting user's APIs"""
    print("\nTesting My APIs endpoint...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/my-apis", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ My APIs retrieved successfully! Found {len(data.get('apis', []))} APIs")
        return True
    else:
        print(f"❌ My APIs failed: {response.status_code} - {response.text}")
        return False

def test_public_apis(token):
    """Test getting public APIs"""
    print("\nTesting Public APIs endpoint...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/public-apis", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Public APIs retrieved successfully! Found {len(data.get('apis', []))} APIs")
        return True
    else:
        print(f"❌ Public APIs failed: {response.status_code} - {response.text}")
        return False

def test_create_api(token):
    """Test creating a new API"""
    print("\nTesting API creation...")
    
    headers = {"Authorization": f"Bearer {token}"}
    api_data = {
        "name": "Test API",
        "description": "A test API endpoint",
        "endpoint_url": "https://jsonplaceholder.typicode.com/posts/1",
        "method": "GET",
        "is_public": True,
        "auth_required": False
    }
    
    response = requests.post(f"{BASE_URL}/apis", json=api_data, headers=headers)
    
    if response.status_code == 201:
        data = response.json()
        print("✅ API created successfully!")
        return data.get('api', {}).get('id')
    else:
        print(f"❌ API creation failed: {response.status_code} - {response.text}")
        return None

def test_call_api(token, api_id):
    """Test calling an API"""
    if not api_id:
        print("\nSkipping API call test (no API ID)")
        return False
        
    print(f"\nTesting API call for API {api_id}...")
    
    headers = {"Authorization": f"Bearer {token}"}
    call_data = {
        "headers": {},
        "params": {},
        "body": {}
    }
    
    response = requests.post(f"{BASE_URL}/apis/{api_id}/call", json=call_data, headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ API call successful! Status: {data.get('response', {}).get('status_code')}")
        return True
    else:
        print(f"❌ API call failed: {response.status_code} - {response.text}")
        return False

def test_stats(token):
    """Test getting API statistics"""
    print("\nTesting API statistics...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/stats", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        stats = data.get('stats', {})
        print(f"✅ Stats retrieved successfully!")
        print(f"   My APIs: {stats.get('my_apis', {}).get('total', 0)}")
        print(f"   Calls made: {stats.get('usage', {}).get('calls_made', 0)}")
        return True
    else:
        print(f"❌ Stats failed: {response.status_code} - {response.text}")
        return False

def main():
    """Run all tests"""
    print("🚀 CNSS API Test Suite")
    print("=" * 40)
    
    # Test login
    token = test_login()
    if not token:
        print("❌ Cannot continue without authentication token")
        return False
    
    # Run API tests
    tests_passed = 0
    total_tests = 5
    
    if test_my_apis(token):
        tests_passed += 1
    
    if test_public_apis(token):
        tests_passed += 1
    
    api_id = test_create_api(token)
    if api_id:
        tests_passed += 1
    
    if test_call_api(token, api_id):
        tests_passed += 1
    
    if test_stats(token):
        tests_passed += 1
    
    print("\n" + "=" * 40)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Your CNSS API is working perfectly.")
        print("\n📝 Next steps:")
        print("1. Open http://localhost:3000 in your browser")
        print("2. Login with admin/admin123 or agent1/agent123")
        print("3. Start managing your APIs!")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to the API server.")
        print("Make sure the Flask backend is running on http://localhost:5000")
        exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        exit(1)
