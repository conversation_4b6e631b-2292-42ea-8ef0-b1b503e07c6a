#!/usr/bin/env python3
"""
Simple test to verify Flask app creation
"""

try:
    print("Testing Flask app creation...")
    from app import create_app
    
    app = create_app()
    print("✅ Flask app created successfully!")
    
    with app.app_context():
        print("✅ App context works!")
        
        # Test database object
        from app import db
        print("✅ Database object accessible!")
        
        # Test models import
        from app.models import User, Employeur, Assure, Beneficiaire
        print("✅ Models imported successfully!")
        
    print("\n🎉 All tests passed! Flask backend is ready.")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
