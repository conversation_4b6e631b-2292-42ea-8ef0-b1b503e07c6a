import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh and errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refresh_token');
        if (refreshToken) {
          const response = await axios.post(
            `${process.env.REACT_APP_API_URL || 'http://localhost:5000/api'}/auth/refresh`,
            {},
            {
              headers: {
                Authorization: `Bearer ${refreshToken}`,
              },
            }
          );

          const { access_token } = response.data;
          localStorage.setItem('access_token', access_token);

          // Retry the original request with new token
          originalRequest.headers.Authorization = `Bearer ${access_token}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  refresh: () => api.post('/auth/refresh'),
  getProfile: () => api.get('/auth/profile'),
  updateProfile: (data) => api.put('/auth/profile', data),
  changePassword: (data) => api.post('/auth/change-password', data),
  register: (data) => api.post('/auth/register', data),
};

// Employeur API
export const employeurAPI = {
  getAll: (params) => api.get('/employeur', { params }),
  getById: (id) => api.get(`/employeur/${id}`),
  create: (data) => api.post('/employeur', data),
  update: (id, data) => api.put(`/employeur/${id}`, data),
  delete: (id) => api.delete(`/employeur/${id}`),
  getStats: () => api.get('/employeur/stats'),
};

// Assure API
export const assureAPI = {
  getAll: (params) => api.get('/assure', { params }),
  getById: (id) => api.get(`/assure/${id}`),
  create: (data) => api.post('/assure', data),
  update: (id, data) => api.put(`/assure/${id}`, data),
  delete: (id) => api.delete(`/assure/${id}`),
  getStats: () => api.get('/assure/stats'),
};

// Beneficiaire API
export const beneficiaireAPI = {
  getAll: (params) => api.get('/beneficiaire', { params }),
  getById: (id) => api.get(`/beneficiaire/${id}`),
  create: (data) => api.post('/beneficiaire', data),
  update: (id, data) => api.put(`/beneficiaire/${id}`, data),
  delete: (id) => api.delete(`/beneficiaire/${id}`),
  getStats: () => api.get('/beneficiaire/stats'),
};

// Admin API
export const adminAPI = {
  getUsers: (params) => api.get('/admin/users', { params }),
  createUser: (data) => api.post('/admin/users', data),
  updateUser: (id, data) => api.put(`/admin/users/${id}`, data),
  deleteUser: (id) => api.delete(`/admin/users/${id}`),
  resetPassword: (id, data) => api.post(`/admin/users/${id}/reset-password`, data),
  getDashboardStats: () => api.get('/admin/dashboard/stats'),
  getSystemHealth: () => api.get('/admin/system/health'),
};

// Logs API
export const logsAPI = {
  getAll: (params) => api.get('/logs', { params }),
  getById: (id) => api.get(`/logs/${id}`),
  getStats: (params) => api.get('/logs/stats', { params }),
  export: (params) => api.get('/logs/export', { params, responseType: 'blob' }),
  getActions: () => api.get('/logs/actions'),
  getResources: () => api.get('/logs/resources'),
};

export default api;
