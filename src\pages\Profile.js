import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from '../components/LoadingSpinner';
import {
  UserCircleIcon,
  KeyIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';

const Profile = () => {
  const { user, updateProfile, changePassword } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');
  const [message, setMessage] = useState({ type: '', text: '' });

  const {
    register: registerProfile,
    handleSubmit: handleSubmitProfile,
    formState: { errors: profileErrors, isSubmitting: isSubmittingProfile },
  } = useForm({
    defaultValues: {
      email: user?.email || '',
      organization: user?.organization || '',
    },
  });

  const {
    register: registerPassword,
    handleSubmit: handleSubmitPassword,
    formState: { errors: passwordErrors, isSubmitting: isSubmittingPassword },
    reset: resetPasswordForm,
  } = useForm();

  const onSubmitProfile = async (data) => {
    try {
      const result = await updateProfile(data);
      if (result.success) {
        setMessage({ type: 'success', text: 'Profil mis à jour avec succès' });
      } else {
        setMessage({ type: 'error', text: result.error });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Erreur lors de la mise à jour du profil' });
    }
  };

  const onSubmitPassword = async (data) => {
    if (data.new_password !== data.confirm_password) {
      setMessage({ type: 'error', text: 'Les mots de passe ne correspondent pas' });
      return;
    }

    try {
      const result = await changePassword({
        current_password: data.current_password,
        new_password: data.new_password,
      });
      
      if (result.success) {
        setMessage({ type: 'success', text: 'Mot de passe modifié avec succès' });
        resetPasswordForm();
      } else {
        setMessage({ type: 'error', text: result.error });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Erreur lors du changement de mot de passe' });
    }
  };

  const tabs = [
    { id: 'profile', name: 'Profil', icon: UserCircleIcon },
    { id: 'password', name: 'Mot de passe', icon: KeyIcon },
  ];

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-secondary-900">Mon Profil</h1>
        <p className="mt-1 text-sm text-secondary-600">
          Gérez vos informations personnelles et paramètres de sécurité
        </p>
      </div>

      {/* Message */}
      {message.text && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className={`p-4 rounded-lg border ${
            message.type === 'success'
              ? 'bg-success-50 border-success-200 text-success-700'
              : 'bg-error-50 border-error-200 text-error-700'
          }`}
        >
          <div className="flex items-center">
            {message.type === 'success' ? (
              <CheckCircleIcon className="h-5 w-5 mr-2" />
            ) : (
              <ExclamationTriangleIcon className="h-5 w-5 mr-2" />
            )}
            {message.text}
          </div>
        </motion.div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <nav className="space-y-1">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => {
                  setActiveTab(tab.id);
                  setMessage({ type: '', text: '' });
                }}
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                  activeTab === tab.id
                    ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-600'
                    : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'
                }`}
              >
                <tab.icon className="mr-3 h-5 w-5" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <div className="card">
            {activeTab === 'profile' && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >
                <div>
                  <h3 className="text-lg font-medium text-secondary-900">
                    Informations du profil
                  </h3>
                  <p className="mt-1 text-sm text-secondary-600">
                    Mettez à jour vos informations personnelles
                  </p>
                </div>

                <form onSubmit={handleSubmitProfile(onSubmitProfile)} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-secondary-700">
                      Nom d'utilisateur
                    </label>
                    <input
                      type="text"
                      value={user?.username || ''}
                      disabled
                      className="mt-1 input-field bg-secondary-50 cursor-not-allowed"
                    />
                    <p className="mt-1 text-xs text-secondary-500">
                      Le nom d'utilisateur ne peut pas être modifié
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-secondary-700">
                      Adresse email
                    </label>
                    <input
                      {...registerProfile('email', {
                        required: 'L\'email est requis',
                        pattern: {
                          value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                          message: 'Adresse email invalide',
                        },
                      })}
                      type="email"
                      className={`mt-1 input-field ${
                        profileErrors.email ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                      }`}
                    />
                    {profileErrors.email && (
                      <p className="mt-1 text-sm text-error-600">{profileErrors.email.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-secondary-700">
                      Organisation
                    </label>
                    <input
                      {...registerProfile('organization')}
                      type="text"
                      className="mt-1 input-field"
                      placeholder="Nom de votre organisation"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-secondary-700">
                      Rôle
                    </label>
                    <input
                      type="text"
                      value={user?.role === 'admin' ? 'Administrateur' : 'Agent'}
                      disabled
                      className="mt-1 input-field bg-secondary-50 cursor-not-allowed"
                    />
                  </div>

                  <div className="pt-4">
                    <button
                      type="submit"
                      disabled={isSubmittingProfile}
                      className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmittingProfile ? (
                        <>
                          <LoadingSpinner size="small" className="mr-2" />
                          Mise à jour...
                        </>
                      ) : (
                        'Mettre à jour le profil'
                      )}
                    </button>
                  </div>
                </form>
              </motion.div>
            )}

            {activeTab === 'password' && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-6"
              >
                <div>
                  <h3 className="text-lg font-medium text-secondary-900">
                    Changer le mot de passe
                  </h3>
                  <p className="mt-1 text-sm text-secondary-600">
                    Assurez-vous d'utiliser un mot de passe fort
                  </p>
                </div>

                <form onSubmit={handleSubmitPassword(onSubmitPassword)} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-secondary-700">
                      Mot de passe actuel
                    </label>
                    <input
                      {...registerPassword('current_password', {
                        required: 'Le mot de passe actuel est requis',
                      })}
                      type="password"
                      className={`mt-1 input-field ${
                        passwordErrors.current_password ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                      }`}
                    />
                    {passwordErrors.current_password && (
                      <p className="mt-1 text-sm text-error-600">{passwordErrors.current_password.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-secondary-700">
                      Nouveau mot de passe
                    </label>
                    <input
                      {...registerPassword('new_password', {
                        required: 'Le nouveau mot de passe est requis',
                        minLength: {
                          value: 6,
                          message: 'Le mot de passe doit contenir au moins 6 caractères',
                        },
                      })}
                      type="password"
                      className={`mt-1 input-field ${
                        passwordErrors.new_password ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                      }`}
                    />
                    {passwordErrors.new_password && (
                      <p className="mt-1 text-sm text-error-600">{passwordErrors.new_password.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-secondary-700">
                      Confirmer le nouveau mot de passe
                    </label>
                    <input
                      {...registerPassword('confirm_password', {
                        required: 'La confirmation du mot de passe est requise',
                      })}
                      type="password"
                      className={`mt-1 input-field ${
                        passwordErrors.confirm_password ? 'border-error-300 focus:border-error-500 focus:ring-error-500' : ''
                      }`}
                    />
                    {passwordErrors.confirm_password && (
                      <p className="mt-1 text-sm text-error-600">{passwordErrors.confirm_password.message}</p>
                    )}
                  </div>

                  <div className="pt-4">
                    <button
                      type="submit"
                      disabled={isSubmittingPassword}
                      className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmittingPassword ? (
                        <>
                          <LoadingSpinner size="small" className="mr-2" />
                          Changement...
                        </>
                      ) : (
                        'Changer le mot de passe'
                      )}
                    </button>
                  </div>
                </form>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
