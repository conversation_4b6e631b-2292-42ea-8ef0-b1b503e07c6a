{"info": {"name": "CNSS API Collection", "description": "Collection for testing CNSS application API endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:5000/api", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('access_token', response.access_token);", "    pm.collectionVariables.set('refresh_token', response.refresh_token);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"admin\",\n  \"password\": \"admin123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}}, {"name": "Get Profile", "request": {"method": "GET", "url": {"raw": "{{base_url}}/auth/profile", "host": ["{{base_url}}"], "path": ["auth", "profile"]}}}, {"name": "Logout", "request": {"method": "POST", "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}}}]}, {"name": "Employeurs", "item": [{"name": "Get All Employeurs", "request": {"method": "GET", "url": {"raw": "{{base_url}}/employeur?page=1&per_page=20", "host": ["{{base_url}}"], "path": ["employeur"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "20"}]}}}, {"name": "Get Employeur by ID", "request": {"method": "GET", "url": {"raw": "{{base_url}}/employeur/1", "host": ["{{base_url}}"], "path": ["employeur", "1"]}}}, {"name": "Create Employeur", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"emp_mat\": 12345678,\n  \"emp_cle\": 12,\n  \"emp_rais\": \"Test Company SARL\",\n  \"emp_tel\": \"0522123456\",\n  \"emp_email\": \"<EMAIL>\",\n  \"emp_activite\": \"Services informatiques\",\n  \"emp_dtaff\": \"2024-01-15\"\n}"}, "url": {"raw": "{{base_url}}/employeur", "host": ["{{base_url}}"], "path": ["employeur"]}}}, {"name": "Update Employeur", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"emp_rais\": \"Updated Company Name\",\n  \"emp_tel\": \"0522654321\"\n}"}, "url": {"raw": "{{base_url}}/employeur/1", "host": ["{{base_url}}"], "path": ["employeur", "1"]}}}, {"name": "Delete Employeur", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/employeur/1", "host": ["{{base_url}}"], "path": ["employeur", "1"]}}}, {"name": "Get Employeur Stats", "request": {"method": "GET", "url": {"raw": "{{base_url}}/employeur/stats", "host": ["{{base_url}}"], "path": ["employeur", "stats"]}}}]}, {"name": "Assures", "item": [{"name": "Get All Assures", "request": {"method": "GET", "url": {"raw": "{{base_url}}/assure?page=1&per_page=20", "host": ["{{base_url}}"], "path": ["assure"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "20"}]}}}, {"name": "Create Assure", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ass_mat\": 87654321,\n  \"ass_cle\": 21,\n  \"emp_mat\": 12345678,\n  \"emp_cle\": 12,\n  \"ass_iu\": 1234567890,\n  \"ass_dteff\": \"2024-01-15\",\n  \"ass_dtimmat\": \"2024-01-15\"\n}"}, "url": {"raw": "{{base_url}}/assure", "host": ["{{base_url}}"], "path": ["assure"]}}}]}, {"name": "Beneficiaires", "item": [{"name": "Get All Beneficiaires", "request": {"method": "GET", "url": {"raw": "{{base_url}}/beneficiaire?page=1&per_page=20", "host": ["{{base_url}}"], "path": ["beneficiaire"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "20"}]}}}, {"name": "Create Beneficiaire", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"ben_iducnss\": 12345678,\n  \"ass_mat\": 87654321,\n  \"ass_cle\": 21,\n  \"ben_nom\": \"<PERSON><PERSON>\",\n  \"ben_prenom\": \"<PERSON>\",\n  \"ben_sexe\": 0,\n  \"ben_dtnais\": \"1990-05-15\"\n}"}, "url": {"raw": "{{base_url}}/beneficiaire", "host": ["{{base_url}}"], "path": ["beneficiaire"]}}}]}, {"name": "Admin", "item": [{"name": "Get Dashboard Stats", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/dashboard/stats", "host": ["{{base_url}}"], "path": ["admin", "dashboard", "stats"]}}}, {"name": "Get All Users", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/users?page=1&per_page=20", "host": ["{{base_url}}"], "path": ["admin", "users"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "20"}]}}}, {"name": "Create User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"newuser\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"role\": \"agent\",\n  \"organization\": \"CNSS Rabat\"\n}"}, "url": {"raw": "{{base_url}}/admin/users", "host": ["{{base_url}}"], "path": ["admin", "users"]}}}, {"name": "System Health", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/system/health", "host": ["{{base_url}}"], "path": ["admin", "system", "health"]}}}]}, {"name": "Logs", "item": [{"name": "Get Audit Logs", "request": {"method": "GET", "url": {"raw": "{{base_url}}/logs?page=1&per_page=50", "host": ["{{base_url}}"], "path": ["logs"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "50"}]}}}, {"name": "Get Log Stats", "request": {"method": "GET", "url": {"raw": "{{base_url}}/logs/stats?days=30", "host": ["{{base_url}}"], "path": ["logs", "stats"], "query": [{"key": "days", "value": "30"}]}}}]}]}