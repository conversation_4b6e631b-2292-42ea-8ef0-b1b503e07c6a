from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models.beneficiaire import Beneficiaire
from app.models.assure import Assure
from app.models.user import User
from app.utils.decorators import agent_or_admin_required, audit_log, validate_json
from datetime import datetime
from sqlalchemy import or_

beneficiaire_bp = Blueprint('beneficiaire', __name__)

@beneficiaire_bp.route('/', methods=['GET'])
@agent_or_admin_required
@audit_log('READ', 'beneficiaire')
def get_beneficiaires():
    """Get all beneficiaires with pagination and search"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '', type=str)
    assure_id = request.args.get('assure_id', type=int)
    
    # Build query
    query = Beneficiaire.query
    
    # Filter by assure if specified
    if assure_id:
        query = query.filter_by(assure_id=assure_id)
    
    # Apply search filter
    if search:
        query = query.filter(
            or_(
                Beneficiaire.numero_beneficiaire.ilike(f'%{search}%'),
                Beneficiaire.cin.ilike(f'%{search}%'),
                Beneficiaire.nom.ilike(f'%{search}%'),
                Beneficiaire.prenom.ilike(f'%{search}%'),
                Beneficiaire.email.ilike(f'%{search}%')
            )
        )
    
    # Apply pagination
    beneficiaires = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'message': 'Beneficiaires retrieved successfully',
        'beneficiaires': [ben.to_dict() for ben in beneficiaires.items],
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': beneficiaires.total,
            'pages': beneficiaires.pages,
            'has_next': beneficiaires.has_next,
            'has_prev': beneficiaires.has_prev
        }
    }), 200

@beneficiaire_bp.route('/<int:id>', methods=['GET'])
@agent_or_admin_required
@audit_log('READ', 'beneficiaire')
def get_beneficiaire(id):
    """Get specific beneficiaire by ID"""
    beneficiaire = Beneficiaire.query.get_or_404(id)
    
    return jsonify({
        'message': 'Beneficiaire retrieved successfully',
        'beneficiaire': beneficiaire.to_dict()
    }), 200

@beneficiaire_bp.route('/', methods=['POST'])
@agent_or_admin_required
@validate_json('numero_beneficiaire', 'nom', 'prenom')
@audit_log('CREATE', 'beneficiaire')
def create_beneficiaire():
    """Create new beneficiaire"""
    data = request.get_json()
    current_user_id = get_jwt_identity()
    
    # Check if numero_beneficiaire already exists
    existing = Beneficiaire.query.filter_by(numero_beneficiaire=data['numero_beneficiaire']).first()
    if existing:
        return jsonify({'message': 'Numero beneficiaire already exists'}), 400
    
    # Check if CIN already exists (if provided)
    if data.get('cin'):
        existing_cin = Beneficiaire.query.filter_by(cin=data['cin']).first()
        if existing_cin:
            return jsonify({'message': 'CIN already exists'}), 400
    
    # Validate assure if provided
    assure = None
    if data.get('assure_id'):
        assure = Assure.query.get(data['assure_id'])
        if not assure:
            return jsonify({'message': 'Assure not found'}), 400
    
    # Create new beneficiaire
    beneficiaire = Beneficiaire(
        numero_beneficiaire=data['numero_beneficiaire'],
        cin=data.get('cin'),
        nom=data['nom'],
        prenom=data['prenom'],
        nom_jeune_fille=data.get('nom_jeune_fille'),
        date_naissance=datetime.strptime(data['date_naissance'], '%Y-%m-%d').date() if data.get('date_naissance') else None,
        lieu_naissance=data.get('lieu_naissance'),
        sexe=data.get('sexe'),
        nationalite=data.get('nationalite'),
        adresse=data.get('adresse'),
        ville=data.get('ville'),
        code_postal=data.get('code_postal'),
        telephone=data.get('telephone'),
        email=data.get('email'),
        assure_id=data.get('assure_id'),
        numero_assure=assure.numero_assure if assure else data.get('numero_assure'),
        lien_parente=data.get('lien_parente'),
        type_beneficiaire=data.get('type_beneficiaire'),
        date_ouverture_droits=datetime.strptime(data['date_ouverture_droits'], '%Y-%m-%d').date() if data.get('date_ouverture_droits') else None,
        statut=data.get('statut', 'actif'),
        numero_carte_soins=data.get('numero_carte_soins'),
        date_emission_carte=datetime.strptime(data['date_emission_carte'], '%Y-%m-%d').date() if data.get('date_emission_carte') else None,
        date_expiration_carte=datetime.strptime(data['date_expiration_carte'], '%Y-%m-%d').date() if data.get('date_expiration_carte') else None,
        created_by=current_user_id,
        updated_by=current_user_id
    )
    
    db.session.add(beneficiaire)
    db.session.commit()
    
    return jsonify({
        'message': 'Beneficiaire created successfully',
        'beneficiaire': beneficiaire.to_dict()
    }), 201

@beneficiaire_bp.route('/<int:id>', methods=['PUT'])
@agent_or_admin_required
@validate_json('nom', 'prenom')
@audit_log('UPDATE', 'beneficiaire')
def update_beneficiaire(id):
    """Update existing beneficiaire"""
    beneficiaire = Beneficiaire.query.get_or_404(id)
    data = request.get_json()
    current_user_id = get_jwt_identity()
    
    # Check if numero_beneficiaire is being changed and if it already exists
    if 'numero_beneficiaire' in data and data['numero_beneficiaire'] != beneficiaire.numero_beneficiaire:
        existing = Beneficiaire.query.filter_by(numero_beneficiaire=data['numero_beneficiaire']).first()
        if existing:
            return jsonify({'message': 'Numero beneficiaire already exists'}), 400
        beneficiaire.numero_beneficiaire = data['numero_beneficiaire']
    
    # Check if CIN is being changed and if it already exists
    if 'cin' in data and data['cin'] != beneficiaire.cin:
        if data['cin']:  # Only check if CIN is not empty
            existing_cin = Beneficiaire.query.filter_by(cin=data['cin']).first()
            if existing_cin:
                return jsonify({'message': 'CIN already exists'}), 400
        beneficiaire.cin = data['cin']
    
    # Validate assure if being changed
    if 'assure_id' in data and data['assure_id'] != beneficiaire.assure_id:
        if data['assure_id']:
            assure = Assure.query.get(data['assure_id'])
            if not assure:
                return jsonify({'message': 'Assure not found'}), 400
            beneficiaire.assure_id = data['assure_id']
            beneficiaire.numero_assure = assure.numero_assure
        else:
            beneficiaire.assure_id = None
            beneficiaire.numero_assure = data.get('numero_assure')
    
    # Update fields
    beneficiaire.nom = data.get('nom', beneficiaire.nom)
    beneficiaire.prenom = data.get('prenom', beneficiaire.prenom)
    beneficiaire.nom_jeune_fille = data.get('nom_jeune_fille', beneficiaire.nom_jeune_fille)
    beneficiaire.lieu_naissance = data.get('lieu_naissance', beneficiaire.lieu_naissance)
    beneficiaire.sexe = data.get('sexe', beneficiaire.sexe)
    beneficiaire.nationalite = data.get('nationalite', beneficiaire.nationalite)
    beneficiaire.adresse = data.get('adresse', beneficiaire.adresse)
    beneficiaire.ville = data.get('ville', beneficiaire.ville)
    beneficiaire.code_postal = data.get('code_postal', beneficiaire.code_postal)
    beneficiaire.telephone = data.get('telephone', beneficiaire.telephone)
    beneficiaire.email = data.get('email', beneficiaire.email)
    beneficiaire.lien_parente = data.get('lien_parente', beneficiaire.lien_parente)
    beneficiaire.type_beneficiaire = data.get('type_beneficiaire', beneficiaire.type_beneficiaire)
    beneficiaire.statut = data.get('statut', beneficiaire.statut)
    beneficiaire.numero_carte_soins = data.get('numero_carte_soins', beneficiaire.numero_carte_soins)
    
    # Update dates
    if data.get('date_naissance'):
        beneficiaire.date_naissance = datetime.strptime(data['date_naissance'], '%Y-%m-%d').date()
    if data.get('date_ouverture_droits'):
        beneficiaire.date_ouverture_droits = datetime.strptime(data['date_ouverture_droits'], '%Y-%m-%d').date()
    if data.get('date_fermeture_droits'):
        beneficiaire.date_fermeture_droits = datetime.strptime(data['date_fermeture_droits'], '%Y-%m-%d').date()
    if data.get('date_emission_carte'):
        beneficiaire.date_emission_carte = datetime.strptime(data['date_emission_carte'], '%Y-%m-%d').date()
    if data.get('date_expiration_carte'):
        beneficiaire.date_expiration_carte = datetime.strptime(data['date_expiration_carte'], '%Y-%m-%d').date()
    
    beneficiaire.updated_by = current_user_id
    beneficiaire.updated_at = datetime.utcnow()
    
    db.session.commit()
    
    return jsonify({
        'message': 'Beneficiaire updated successfully',
        'beneficiaire': beneficiaire.to_dict()
    }), 200

@beneficiaire_bp.route('/<int:id>', methods=['DELETE'])
@agent_or_admin_required
@audit_log('DELETE', 'beneficiaire')
def delete_beneficiaire(id):
    """Delete beneficiaire"""
    beneficiaire = Beneficiaire.query.get_or_404(id)
    
    db.session.delete(beneficiaire)
    db.session.commit()
    
    return jsonify({'message': 'Beneficiaire deleted successfully'}), 200

@beneficiaire_bp.route('/stats', methods=['GET'])
@agent_or_admin_required
@audit_log('READ', 'beneficiaire')
def get_beneficiaire_stats():
    """Get beneficiaire statistics"""
    total_beneficiaires = Beneficiaire.query.count()
    active_beneficiaires = Beneficiaire.query.filter_by(statut='actif').count()
    inactive_beneficiaires = total_beneficiaires - active_beneficiaires
    
    # Get beneficiaires by relationship type
    relationships = db.session.query(
        Beneficiaire.lien_parente,
        db.func.count(Beneficiaire.id).label('count')
    ).group_by(Beneficiaire.lien_parente).all()
    
    return jsonify({
        'message': 'Beneficiaire statistics retrieved successfully',
        'stats': {
            'total': total_beneficiaires,
            'active': active_beneficiaires,
            'inactive': inactive_beneficiaires,
            'by_relationship': [{'relationship': r[0], 'count': r[1]} for r in relationships if r[0]]
        }
    }), 200
